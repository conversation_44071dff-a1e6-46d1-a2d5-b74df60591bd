<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.excelfore</groupId>
    <artifactId>brp-thin-client</artifactId>
    <packaging>pom</packaging>
    <name>BRP Thin Client</name>
    <version>1.0.0-SNAPSHOT</version>

    <repositories>

        <repository>
            <id>excelfore.all</id>
            <name>ESync Development repo</name>
            <url>https://dev-esync.excelfore.com/artifactory/excelfore.all</url>
        </repository>

    </repositories>

    <scm>
        <url>https://gitlab.excelfore.com/PCC-AED/esync-agent</url>
        <connection>scm:git:https://gitlab.excelfore.com/PCC-AED/esync-agent.git</connection>
        <developerConnection>scm:git:************************:PCC-AED/esync-agent.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <distributionManagement>
        <repository>
            <id>excelfore.private</id>
            <name>Excelfore Repository</name>
            <url>https://dev-esync.excelfore.com/artifactory/excelfore.private</url>
        </repository>
    </distributionManagement>

    <properties>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- we use JDK 11 because that's what Gradle is using, so we know we have it -->
        <java.version>11</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.version>3.10.1</maven.compiler.version>
        <maven.plugin-api.version>3.8.5</maven.plugin-api.version>
        <maven.plugin.version>3.6.4</maven.plugin.version>

    </properties>

    <modules>
        <module>sbom-verify</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-api</artifactId>
                <version>${maven.plugin-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-compat</artifactId>
                <version>${maven.plugin-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-core</artifactId>
                <version>${maven.plugin-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.maven.plugin-tools</groupId>
                <artifactId>maven-plugin-annotations</artifactId>
                <version>${maven.plugin.version}</version>
            </dependency>

            <dependency>
                <groupId>com.networknt</groupId>
                <artifactId>json-schema-validator</artifactId>
                <version>1.3.3</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>


