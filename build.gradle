import com.excelfore.esync.agent.bld.sbom.SBomber

buildscript {
    ext {
        kotlin_version = '2.0.21'
    }
    repositories {
        mavenCentral()
        google()
        mavenLocal()
        maven() {
            name 'excelforeAll'
            credentials(PasswordCredentials)
            url 'https://dev-esync.excelfore.com/artifactory/excelfore.all'
            authentication {
                basic(BasicAuthentication)
            }
        }
    }
    dependencies {
        classpath 'com.google.android.gms:oss-licenses-plugin:0.10.6'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id 'com.gladed.androidgitversion' version '0.4.13'
    id("com.android.application") version '8.7.2' apply false
    id("org.owasp.dependencycheck") version "7.4.4" apply false
}

def VERSION = new XmlSlurper().parseText(new File("${project.rootDir}/pom.xml").text).version.text().trim()
if (VERSION == null || "" == VERSION) {
    throw new GradleException("Version is null/empty")
}

def releaseVersionCode = 0
try {
    releaseVersionCode = Integer.parseInt(System.getenv("BUILD_NUMBER"))
    logger.lifecycle("verion code specified by Jenkins : "+releaseVersionCode)
} catch (ignored){}
def localVersionCode = 0
def releaseVersionName = VERSION
if (releaseVersionName != null) {
    releaseVersionName = releaseVersionName.trim()
    if ("" == releaseVersionName) { releaseVersionName = null }
}

if (releaseVersionName == null || releaseVersionName.length() > 32) {
    throw new GradleException("Release version must not be null, and must be 32 characters or shorter. I have >>"+releaseVersionName+"<<")
}

if (releaseVersionCode == 0) {

    // if releaseVersionCode is 0, this means there was no BUILD_NUMBER
    // environment variable, and we are not being build in Jenkins,
    // but in a development workspace of sorts. Add the "-nj" (not Jenkins)
    // suffix to the name, to avoid clashes with Jenkins builds
    releaseVersionName += "-nj"

    // https://stackoverflow.com/a/21405744/622266
    def versionPropsFile = file('auto-vc-increase.txt')
    Properties versionProps = new Properties()

    try {
        versionProps.load(new FileInputStream(versionPropsFile))
        localVersionCode = versionProps['VERSION_CODE'].toInteger()
    } catch (ignored) {}

    localVersionCode++
    versionProps['VERSION_CODE'] = localVersionCode.toString()
    versionProps.store(versionPropsFile.newWriter(), null)

    releaseVersionCode = localVersionCode

}

logger.lifecycle("verion name : "+releaseVersionName)
logger.lifecycle("verion code : "+releaseVersionCode)

def stdVersions = [
        guava: "32.0.1-android",
        room: "2.2.6",
        gson: "2.9.0",
        volley: "1.2.1",
        okhttp: "4.9.3",
        annotation: "1.1.0",
        bc: "1.78",
        material: "1.3.0",
        constraintLayout: "2.1.4",
        // Due to this, we need to use the latest version
        // even thought it's "alpha".
        // https://stackoverflow.com/q/63093672/622266
        securityCrypto: "1.1.0-alpha06",
        tape2: "2.0.0-beta1",
        appCompat: "1.6.1",
        // kotlin dependencies, which are used by okio, which is
        // used by okhttp, are vulnerable, so we need to force a
        // higher version of kotlin-stdlib
        kotlinStdLib: '1.6.10',
        // Upgrade okio to fix CVE-2023-3635
        okio: '3.4.0',
        ktx : '1.10.1',

        test: [
                junit: "4.13.2",
                robolectric: "4.9.2",
                core: "1.3.0",
                fake_keystore: "2.0.1",
                nano: "2.3.4",
                coreServer: "4.2327-RC1",
                junit5: "5.8.2",
                junit_platform: "1.8.2",
                commonsIO: "2.6",
                room_testing: "2.2.6",
                jakarta_xml_bind: "2.3.2",
                jaxb: "2.3.2",
                json_simple: "1.1.1",
                web_token: "0.11.2",
                json_canonicalization: "1.1",
                gson_pointer: "0.4",
        ]
]

subprojects {
    ext {
        useVersionName = releaseVersionName
        useVersionCode = releaseVersionCode
        versions = stdVersions
    }
}

androidGitVersion {
    // baseCode 1
    untrackedIsDirty = false
}

allprojects {

    it.repositories {
        google()
        mavenCentral()
        mavenLocal()
        maven { url "https://jitpack.io" }
        maven {
            name 'excelforeAll'
            credentials(PasswordCredentials)
            url 'https://dev-esync.excelfore.com/artifactory/excelfore.all'
            authentication {
                basic(BasicAuthentication)
            }
        }
    }

}

task clean(type: Delete) {
    delete rootProject.buildDir
    delete new File(rootProject.projectDir, "buildSrc/build")
}

afterEvaluate {

    tasks.register("dropSBom", SBomber.class) {
        depJsonFile new File("./app/build/generated/third_party_licenses/v1Debug/dependencies.json")
        sBomFile new File("./build/sbom.json")
        pkgDir new File("./build")
        topDir new File("./")
        version VERSION
    }

    tasks.register('printVersion') {
        it.doFirst {
            println "VERSION=" + VERSION
        }
    }

}


configurations.all {
    resolutionStrategy.cacheDynamicVersionsFor 0, "seconds"
}

