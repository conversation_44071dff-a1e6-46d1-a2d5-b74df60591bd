
======= BRP eSync OTA Client Delivery =======

Contents:

1. This README file
2. eSync OTA client APK (debug variant)
3. System permissions file

Conditions of the delivery:

THE DELIVERY IS A POC GRADE CODE AND CAN NOT
BE INSTALLED IN PRODUCTION DEVICES. SPECIFICALLY,
THE DELIVERED CODE DOES NOT HAVE A PROPERTY
SECURITY MODEL FOR:
A) SECURING AUTHENTICATION CREDENTIALS
B) SECURING UPDATE OPERATIONS

Installation:

NOTE! This section assumes the operator has extensive knowledge
on manual APK installation process.

- APK included in the package is signed by Android SDK debug keys

- APK must be signed by the relevant keys if the platform requires
  APKs signed by the specific roots of trust.

- APK installation process is specifically described below, and must
  be followed as specified, after being adjusted with a particular
  environment.

Preconditions:

1. ADB 'remount' command should show successful output
   indicating system partitions are mount in read-write mode.
   Specifically, /system and /data partitions must be writable.

2. ADBD must me running in root mode

Installations:

IMPORTANT: The steps below must be repeated every time a demo
needs to be reset.

1. Copy (adb push or similar) the permissions_com.excelfore.esync.brp.xml
     file to the /etc/permissions location on the device
   The file is provided in the release package

2. Ensure /system/priv-app/brp-esync/ location exists on the device
     and is a directory.

3. Copy (adb push or similar) the brp-esync.apk file to
     the /system/priv-app/brp-esync/ location on the device
   The file is provided in the release package

4. Execute the following commands (adb shell or similar) on the device:
# begin
chmod 755 /system/priv-app/brp-esync
chmod 644 /system/priv-app/brp-esync/brp-esync.apk
am force-stop com.excelfore.esync.brp
pm uninstall com.excelfore.esync.brp
rm -rf /data/data/com.excelfore.esync.brp/
mkdir -p /data/swupdate
chmod 777 /data/swupdate
# this last commands remove any previously "installed" KSS files,
# and ensures that the client downloads the update anew.
rm -rf /data/swupdate/*
# end

5. Reboot the device (IMPORTANT REQUIREMENT)

6. Ensure that the APK shortcut is placed accessibly
   on the Android top, e.g., drag and drop the Excelfore
   APK link to an Android main screen

7. Start the OTA client.

Functionality:

At this point, the application will:

1. Display the application identity information in an UI activity when launched.
   Show a toggle switch to control the mechanism used to notify the installer.
   See step 7 on the details of the impact the toggle switch has.
2. Start the internal OTA client engine, with the identity of device ID "24260001079"
   and connecting to OTA server at https://brp-esync.excelfore.com
3. Discover the update. Once discovered, the UI will show a "Update available: XXX"
   text, where XXX is the version corresponding to the update.
4. Download the update logging the progress in the Android Log and displaying
   the download progress on its UI as a red filling horizontal progress bar.
   First 50% of the progress measures the download from the eSync backend,
   the second 50% measures the decryption of the downloaded binary.
4.1. During the download and decryption, the text views below the download progress
     bar will update with the effective byte per second rate, with the total time spent
     on the download. The rates displayed (left-to-right, top-to-bottom):
     * Waiting/reading the network
     * Reading for decryption
     * Writing downloaded data
     * Writing for decryption
     * Overall effective download
     * Overall effective decryption
5. Once the download is completed, a two-button dialog for installation consent is
   provided. Not clicking any button will have the OTA client continue waiting on a
   button input.
6a. Activating "Cancel Update" button will dismiss the buttons. The update is then
    considered failed, which can be observed in the logs, and on the eSync Campaign
    Frontend. No further activity is possible.
6b. Activating "Yes, Install" button will dismiss the buttons, and start the
    update process. A red-filling horizontal progress bar will be displayed
    to show the progress of unzipping the KSS files to the target location.
7. The OTA client will:
  * Extract all KSS files, into the /data/swupdate/kss_bins directory
  * Extract android payload file and corresponding config file into the
    /data/swupdate/ota/ directory
  * Re-write the json file to have its URL pointing to the downloaded .zip OTA file.
8a. If the switch toggle is in "Use AM" position, the OTA client invokes
        am start -a android.intent.action.SEND -t text/plain --es android.intent.extra.TEXT android-ota-full com.example.android.systemupdatersample
        and schedule another internal session.
8b. If the switch toggle is in "Use Intent" position, the OTA client creates the
    intent matching the specified 'am start' command, submits it to the activity manager
    and schedule another internal session. The intent will additionally have an extra
    property com.example.android.systemupdatersample.CONFIG with the contents of the JSON file.
9. The next update session will display an additional "Update completed" message
   upon detecting the presence of previously unpacked KSS files.

IMPORTANT!
    We could not fully test the dispatching of the intent to the sample update manager.
    Using 'am' command fails with the following exception reported by 'am'. It's unlikely
    that this approach can ever work.

        W ActivityTaskManager: Permission Denial: package=com.android.shell does not belong to uid=10126
        I eSync   : am stdout: Starting: Intent { act=android.intent.action.SEND typ=text/plain pkg=com.example.android.systemupdatersample (has extras) }
        I eSync   : -- stdout EOF from am
        I eSync   : am stderr:
        I eSync   : am stderr: Exception occurred while executing 'start':
        I eSync   : am stderr: java.lang.SecurityException: Permission Denial: package=com.android.shell does not belong to uid=10126
        I eSync   : am stderr: 	at com.android.server.wm.ActivityTaskManagerService.assertPackageMatchesCallingUid(ActivityTaskManagerService.java:2247)
        I eSync   : am stderr: 	at com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1264)
        I eSync   : am stderr: 	at com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1233)
        I eSync   : am stderr: 	at com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3139)
        I eSync   : am stderr: 	at com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:603)
        I eSync   : am stderr: 	at com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:208)
        I eSync   : am stderr: 	at com.android.modules.utils.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:97)
        I eSync   : am stderr: 	at android.os.ShellCommand.exec(ShellCommand.java:38)
        I eSync   : am stderr: 	at com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:9197)
        I eSync   : am stderr: 	at android.os.Binder.shellCommand(Binder.java:1049)
        I eSync   : am stderr: 	at android.os.Binder.onTransact(Binder.java:877)
        I eSync   : am stderr: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:4731)
        I eSync   : am stderr: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2630)
        I eSync   : am stderr: 	at android.os.Binder.execTransactInternal(Binder.java:1285)
        I eSync   : am stderr: 	at android.os.Binder.execTransact(Binder.java:1244)
        I eSync   : -- stderr EOF from am
        E eSync   : APK installation session failed
        E eSync   : java.lang.Exception: am finished with non-0 ec 255
        E eSync   : 	at com.excelfore.esync.brp.installer.Installer.lambda$irrevocableUpdate$5$com-excelfore-esync-brp-installer-Installer(Installer.java:180)
        E eSync   : 	at com.excelfore.esync.brp.installer.Installer$$ExternalSyntheticLambda10.run(D8$$SyntheticClass:0)
        E eSync   : 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:463)
        E eSync   : 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
        E eSync   : 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
        E eSync   : 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
        E eSync   : 	at java.lang.Thread.run(Thread.java:1012)

    Sending the intent to the activity manager produces the following exception,
    as we don't have software that would listen to the intent:

        E eSync   : android.content.ActivityNotFoundException: No Activity found to handle Intent { act=android.intent.action.SEND typ=text/plain flg=0x1 pkg=com.example.android.systemupdatersample clip={text/plain {T(16)}} (has extras) }
        E eSync   : 	at android.app.Instrumentation.checkStartActivityResult(Instrumentation.java:2200)
        E eSync   : 	at android.app.Instrumentation.execStartActivity(Instrumentation.java:1839)
        E eSync   : 	at android.app.Activity.startActivityForResult(Activity.java:5471)
        E eSync   : 	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:728)
        E eSync   : 	at android.app.Activity.startActivityForResult(Activity.java:5429)
        E eSync   : 	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:709)
        E eSync   : 	at android.app.Activity.startActivity(Activity.java:5927)
        E eSync   : 	at android.app.Activity.startActivity(Activity.java:5894)
        E eSync   : 	at com.excelfore.esync.brp.installer.Installer.lambda$irrevocableUpdate$5$com-excelfore-esync-brp-installer-Installer(Installer.java:190)
        E eSync   : 	at com.excelfore.esync.brp.installer.Installer$$ExternalSyntheticLambda10.run(D8$$SyntheticClass:0)
        E eSync   : 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:463)
        E eSync   : 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
        E eSync   : 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
        E eSync   : 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
        E eSync   : 	at java.lang.Thread.run(Thread.java:1012)

    We expect that the standard activity intent invocation will work given that a
    com.example.android.systemupdatersample application is installed on the HU.

NOTE! The OTA client engine is the core implementation of eSync OTA client functionality.
      Not to be confused with the Android Update Engine

IMPORTANT!
    The OTA client must be reinstalled, according to the instructions in this document,
      to re-run the demo scenarios
    Otherwise:
    * The client will not re-provision itself (saved provisioning information is valid)
    * The client will not re-download the update (already downloaded)
    * The client will not submit the update for installation (already considered installed or failed)

Logging:

When examining the logs, only consider log messages tagged by the OTA client; the tag
is either literal "eSync" or starts with the literal "eSync". Logging messages from other
tags, even if reported against the OTA client application ID (com.excelfore.esync.brp), are
not directly produced by our code. For any information behind the meaning and reasons of those
messages, unless the messages report a crash, please refer to your Android build vendor.

The typical client log output, as of now, will be:

*** Start-up, reporting initialization

I  Starting BRP eSync client untagged-5-9ae0d5d-dirty(release 1.0.0-SNAPSHOT-nj)(22)
I  Running id:810
V  Locking database (Loading Settings(R))
V  Database locked (Loading Settings(R))
I  Building database...
I  Migrating the database
V  Database built
V  Database verified
W  No properties found in the database
V  Unlocking database (Loading Settings(R))

*** Initialization. Shows identity and certificate creation

W  CRL is not present
I  device id: 24260001079
I  ota url: https://brp-esync.excelfore.com:8443/snap/oma
V  Unlocking database (Saving settings(W))
I  Certificate info was changed, deleting current key/certificate
E  Failed to delete key/certificate file /data/user/0/com.excelfore.esync.brp/files/client.key, /data/user/0/com.excelfore.esync.brp/files/client.cert

*** Start OTA engine cycle

V  SSR origin for Application ready:811
I  Scheduling <810:811>[Application ready]@28,493,714(in 998ms)
I  Delaying <810:811>[Application ready]@28,493,714(in 996ms) in 996ms
D  Cleaning the log messages finished and took 5000ms
I  Submitting <810:811>[Application ready]@28,493,714(od by 4,005ms) for execution
I  Considering session request <810:811>[Application ready]@28,493,714(od by 4,006ms)
D  Cycling reports
I  Reporting sleeping for null

*** First communication to the backend triggers certificate creation
    NOTE! The certificate is only created during the first run of the client, after installation
    or re-installation

D  UpdateState: otaURL: changed null -> https://brp-esync.excelfore.com:8443/snap/oma
D  HTTP->POST->https://brp-esync.excelfore.com:8443/snap/dmclient/js/v1/sync/check-in->{"fingerprint":"google\/sdk_gphone64_x86_64\/emu64x:13\/TE1A.240213.009\/12342917:user\/release-keys"}
I  Generating PKI objects
I  Generating new device key/certificate
D  Generating new keypair (may take a while)
D  Keypair generated
D  Saving PEM for client.cert
D  HTTP<-200<-649ms<-{"config":{"elements":[{"path":".\/WSApp\/Config\/PublicURLs","type":"BOOLEAN","value":false},{"path":".\/WSApp\/Config\/OMAReadTimeout","type":"NUMBER","value":10000},{"path":".\/WSApp\/Config\/DownloadConnectTimeout","type":"NUMBER","value":10000},{"path":".\/WSApp\/Config\/DownloadReadTimeout","type":"NUMBER","value":10000},{"path":".\/WSApp\/Config\/DownloadSkipTimeout","type":"NUMBER","value":50},{"path":".\/WSApp\/Config\/CampaignMode","type":"BOOLEAN"}]}}
D  Reported server version: core:6.0(esync-release-6.0)

*** Update discovery

I  Will accept new updates, as there is no current target
D  HTTP->POST->https://brp-esync.excelfore.com:8443/snap/dmclient/js/v1/sync/check->{"current":[{"noUpdate":false,"stateSequence":1,"node":"BRP-POC","version":"google\/sdk_gphone64_x86_64\/emu64x:13\/TE1A.240213.009\/12342917:user\/release-keys"}],"reportedOnly":true}
D  HTTP<-200<-503ms<-{"targets":[{"policies":{"TIME_WINDOW":{"type":"TIME_WINDOW","source":"none"}},"campaignId":"1050","recordId":"1050","maxRetries":3,"stateSequence":23,"target":{"display":"BRP PoC Update","type":"\/THIN\/POC","node":"BRP-POC","version":"1.0.alpha","download":{"method":"AES\/CTR\/NoPadding","key":"--censored--","sha256":"Pp4sXuf9mwDH5elyr9KbE2mZBGHMfUwGvYLxbJp\/kbw=","size":4199101,"id":"f1051","url":"https:\/\/brp-esync.excelfore.com:8443\/snap\/odl\/pkg-i\/1051"}},"aborted":false}]}
D  UpdateState changed - target replaced
   Old contents: null
   New contents:{"aborted":false,"campaignId":"1050","logOnFail":false,"maxRetries":3,"recordId":"1050","stateSequence":23,"storageDuration":0,"target":{"display":"BRP PoC Update","download":{"key":{"iv":"/VlUWuLlzZJisKuTQo6Qew==","data":"snHO2L8emumCVK2NvWobHw==","hash":"m6XTZf43PFQuRjC06XUHwBspvy4WXOxBlZs2CnMNNOU="},"method":"AES/CTR/NoPadding","sha256":"Pp4sXuf9mwDH5elyr9KbE2mZBGHMfUwGvYLxbJp/kbw=","size":4199101,"url":"https://brp-esync.excelfore.com:8443/snap/odl/pkg-i/1051"},"type":"/THIN/POC","node":"BRP-POC","version":"1.0.alpha"}}
D  Saving state:{"otaURL":"https://brp-esync.excelfore.com:8443/snap/oma","progress":{"attempted":0,"downloadConsentRequest":0,"downloadSize":0,"downloaded":false,"hadAttempt":false,"haveConsent":false,"haveInstallConsent":false,"hmiSeen":false,"installConsentRequest":0,"lastKnownDownloadProgress":0,"prnShown":false,"releaseNotesReceived":false,"sourceVersion":"google/sdk_gphone64_x86_64/emu64x:13/TE1A.240213.009/12342917:user/release-keys","targetVersion":"1.0.alpha","unusable":false,"updateApplied":false,"updateSubmitted":false,"verificationFailures":0,"verifiedTimestamp":0},"report":{"componentState":10,"id":"1050","reported":"2024-11-21T14:32:19.803+01:00","reportedVersion":"google/sdk_gphone64_x86_64/emu64x:13/TE1A.240213.009/12342917:user/release-keys","sequence":"0","stateSequence":24,"terminal":false,"updateState":252,"url":"https://brp-esync.excelfore.com:8443/snap/oma","userStatus":1},"stateSequence":24,"target":{"aborted":false,"campaignId":"1050","logOnFail":false,"maxRetries":3,"recordId":"1050","stateSequence":23,"storageDuration":0,"target":{"display":"BRP PoC Update","download":{"key":{"iv":"/VlUWuLlzZJisKuTQo6Qew==","data":"snHO2L8emumCVK2NvWobHw==","hash":"m6XTZf43PFQuRjC06XUHwBspvy4WXOxBlZs2CnMNNOU="},"method":"AES/CTR/NoPadding","sha256":"Pp4sXuf9mwDH5elyr9KbE2mZBGHMfUwGvYLxbJp/kbw=","size":4199101,"url":"https://brp-esync.excelfore.com:8443/snap/odl/pkg-i/1051"},"type":"/THIN/POC","node":"BRP-POC","version":"1.0.alpha"}}}

*** Update download

D  PUBLIC_URL->false
I  Download URL is private, applying custom trust manager/authentication
I  Download continuation is not possible, no saved ETag found
I  Will download to /data/user/0/com.excelfore.esync.brp/binaries/download/1.0.alpha.ct, from https://brp-esync.excelfore.com:8443/snap/odl/pkg-i/1051
D  componentState: componentState: changed IDLE -> DOWNLOAD_PROGRESSING
D  Truncating downloaded file to 0, since performing full download
I  Saving eTag value "f4f64ddee68588d4584f1dcb5bec259657548a5e7276094a0d32789cb05f506d" from incoming headers
I  Bytes to download: 4199104
D  componentState: downloadPercent: changed 0 -> 10
D  componentState: downloadPercent: changed 10 -> 20
D  componentState: downloadPercent: changed 20 -> 30
D  componentState: downloadPercent: changed 90 -> 100
D  componentState: error: changed null -> Download complete:  (downloaded 4,199,104 range 0 to 4,199,103, len 4,199,104)
D  componentState: componentState: changed DOWNLOAD_PROGRESSING -> DOWNLOAD_COMPLETE

*** Decrypting and verifying the downloaded binary

I  Decrypting binary, buffer:16384
I  Decryption finished
I  PKCS7 verification successful
I  Verifier finished with :null
D  progress: verifiedTimestamp: changed 0 -> 1732195945000
D  progress: downloadFileState: changed CREATED -> DELETED_ON_COMPLETE
D  componentState: error: changed Download complete:  (downloaded 4,199,104 range 0 to 4,199,103, len 4,199,104) -> Sending installation to update engine

*** Asking for the installation consent

D eSync   : componentState: error: changed Download complete:  (downloaded 56,953,936 range 0 to 56,953,935, len 56,953,936) -> Requesting consent from the end-user
D eSync   : componentState: componentState: changed DOWNLOAD_COMPLETE -> READY_TO_UPDATE

*** Consent provided, applying the update

D eSync   : progress: consent: changed null -> true
I eSync   : Consent now provided for 1109 - true
V eSync   : SSR origin for Consent provided:2
D eSync   : File /data/user/0/com.excelfore.esync.brp/binaries/download/297.v2.pt has not changed since last verification
D eSync   : progress: updateSubmitted: changed false -> true
D eSync   : componentState: error: changed Requesting consent from the end-user -> Sending installation to update engine
D eSync   : componentState: componentState: changed READY_TO_UPDATE -> UPDATE_PROGRESSING
I eSync   : Unzipping kss_bins/input_b.json to /data/swupdate/kss_bins/input_b.json
I eSync   : Unzipping kss_bins/2W_KSS_BL.BIN to /data/swupdate/kss_bins/2W_KSS_BL.BIN
I eSync   : Unzipping kss_bins/2W_KSS_APP.BIN to /data/swupdate/kss_bins/2W_KSS_APP.BIN
I eSync   : Unzipping kss_bins/bl2.bin to /data/swupdate/kss_bins/bl2.bin
I eSync   : Unzipping kss_bins/input_a.json to /data/swupdate/kss_bins/input_a.json
I eSync   : Unzipping kss_bins/r7loader.bin to /data/swupdate/kss_bins/r7loader.bin
I eSync   : Unzipping files/android-ota-full.zip to /data/swupdate/ota/android-ota-full.zip
I eSync   : Unzipping configs/android-ota-full.json to /data/swupdate/ota/android-ota-full.json


**** With executing 'am' command

I eSync   : am stdout: Starting: Intent { act=android.intent.action.SEND typ=text/plain pkg=com.example.android.systemupdatersample (has extras) }
I eSync   : -- stdout EOF from am

**** With using Intent
I eSync   : Intent sent

*** Determining that the update is considered applied

I eSync   : Considering /data/swupdate/kss_bins/input_b.json - looks OK
I eSync   : Considering /data/swupdate/kss_bins/2W_KSS_BL.BIN - looks OK
I eSync   : Considering /data/swupdate/kss_bins/2W_KSS_APP.BIN - looks OK
I eSync   : Considering /data/swupdate/kss_bins/bl2.bin - looks OK
I eSync   : Considering /data/swupdate/kss_bins/input_a.json - looks OK
I eSync   : Considering /data/swupdate/kss_bins/r7loader.bin - looks OK
I eSync   : Have 6 or more files, declaring as updated
I eSync   : Assuming version as target: v2

*** Consent denied, failing the update

D eSync   : huf: msg=com.excelfore.esync.brp.model.sync_js.ClientMessage@9669582, us=FAILED, cs=UPDATE_FAILED_WITHOUT_DATA, dl=false
D eSync   : componentState: error: changed Requesting consent from the end-user -> Installation consent denied
D eSync   : componentState: updateState: changed PROGRESS -> FAILED
D eSync   : componentState: componentState: changed READY_TO_UPDATE -> UPDATE_FAILED_WITHOUT_DATA
D eSync   : componentState: failureCount: changed null -> 0


IMPORTANT!
   Only excerpts of the logs are showcased above. The client will produce more logs that
   are not shown for brevity and lack of relevance.

   The client may log errors that are recoverable, and are logged for debugging purposes,
   as long as all steps are followed, such logs do not indicate that there is an actual issue.

   The log messages shown as patterns, the actual data displayed in observed log
   messages may significantly differ based on the current update set up on the backend,
   and the device environment.
