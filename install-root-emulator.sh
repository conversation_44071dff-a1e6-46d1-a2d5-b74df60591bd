#!/bin/bash

# https://stackoverflow.com/a/31703543/622266

if test -z "$SDK"; then
    SDK="$HOME/Android/Sdk"
fi

do_build=true
do_sign=false

export OPTERR=1

gen=

echo "Use $0 -? for help"

function help() {
    cat << _EOF_
$0 [-ohb] [-O path] [-H path]

Options:
-?
  This text
-s
  Sign OTA client APK with platform.p12
-b
  Do not invoke build command(s)
-O path
  Specify alternative location for OTA client APK
-G opts
  extra gradle options, separated by space

_EOF_
    exit 1
}

while getopts "g:ohb?O:H:G:f:l:s" arg; do
    case $arg in
        b) do_build=false ;;
        O) ext_apk="$OPTARG" ;;
        G) gopts="$OPTARG" ;;
        s) do_sign=true ;;
        '?') help; ;;
    esac
done

apk=V1

if test -n "$ext_apk"; then
    apk_host="$ext_apk"
else
    apk_host="./app/build/outputs/apk/$(echo $apk|tr 'A-Z' 'a-z')/debug/brp-esync.apk"
    # Delete previous APK
    $do_build && rm -f "$apk_host"
fi

# CHANGE THESE FOR YOUR APP
app_package="com.excelfore.esync.brp"
dir_app_name="brp-esync"

ADB="$SDK/platform-tools/adb" # how you execute adb
function ADB_SH() {
    echo "Executing $ADB shell $*"
    "$ADB" shell "$@"
}
# ADB_SH="$ADB shell" # this script assumes using `adb root`. for `adb su` see `Caveats`
function ADB() {
    echo "Executing $ADB $*"
    "$ADB" "$@"
}

path_sysapp="/system/priv-app" # assuming the app is privileged
apk_name=$dir_app_name".apk"
apk_target_dir="$path_sysapp/$dir_app_name"
apk_target_sys="$apk_target_dir/$apk_name"

# do this early, so whatever time ADB needs to
# reconnect, is spent while we are building
$ADB root 2> /dev/null

# Compile the APK: you can adapt this for production build, flavors, etc.
# ./gradlew assembleDebug || exit -1 # exit on failure
$do_ota && $do_build && { ./gradlew app:assemble${apk} $gopts || exit -1; } # exit on failure

# Install APK: using adb root
$ADB remount # mount system
# https://gitlab.com/AuroraOSS/AuroraServices/-/issues/6
ADB push release_files/permissions_com.excelfore.esync.brp.xml /etc/permissions/

$do_sign && {
    signer="$(find "$SDK"/ -name apksigner | head -1)"
    if test -z "$signer"; then
        echo "No apksigner binary found"
        exit 1;
    fi
    echo "*** SIGNING..."
    "$signer" sign --ks platform.p12 --ks-pass pass:android --ks-key-alias platform --key-pass pass:android "$apk_host" || exit 1
    echo "*** SIGNED"
}

$do_ota && ADB push "$apk_host" "$apk_target_sys"

# Give permissions
ADB_SH "chmod 755 $apk_target_dir"
ADB_SH "chmod 644 $apk_target_sys"

# Stop the app
$do_ota && ADB_SH "am force-stop $app_package"
$do_ota && ADB_SH "pm uninstall $app_package"
$do_ota && ADB_SH "rm -rf /data/data/com.excelfore.esync.brp/"
$do_ota && ADB_SH "mkdir -p /data/swupdate"
$do_ota && ADB_SH "rm -rf /data/swupdate/*"
$do_ota && ADB_SH "chmod 777 /data/swupdate"

#Unmount system $TODO: WHAT FOR?
$do_ota && ADB_SH "mount -o remount,ro /"


# Re execute the app
# $ADB shell "am start -n \"$app_package/$app_package.$MAIN_ACTIVITY\" -a android.intent.action.MAIN -c android.intent.category.LAUNCHER"

