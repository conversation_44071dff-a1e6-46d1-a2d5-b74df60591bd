#!/usr/bin/env bash

set -e

DOCKER_IMAGE_NAME="esync.thin.client.build:$$"
ENV_FILE="$(mktemp)"
LOCAL_PROP="$(mktemp)"

trap cleanup EXIT 2 3 15

function cleanup() {
  :
  docker rmi -f "$DOCKER_IMAGE_NAME"
  rm -f "$ENV_FILE"
  rm -f "$LOCAL_PROP"
}

cat > "$ENV_FILE" << _EOF_
ORG_GRADLE_PROJECT_excelforeAllUsername=$excelforeAllUsername
ORG_GRADLE_PROJECT_excelforeAllPassword=$excelforeAllPassword
_EOF_

lGradle=~/.gradle/gradle.properties

if test -f "$lGradle"; then
    cat "$lGradle" >> "$ENV_FILE"
fi

# do not set -x before this line, or this will dump the password
# into the build log!
set -x

THIS_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TOP_DIR="$(cd "$THIS_DIR"/.. && pwd)"
BUILD_DIR="$TOP_DIR/build"

mkdir -p "$BUILD_DIR"

uid="$(id -u)"
gid="$(id -g)"
DOCKER_FILE="$BUILD_DIR"/Dockerfile
# 
# See https://jenkins.excelfore.com/job/android-sdk-docker/
# for info on how the used docker image is made
cat > "$DOCKER_FILE" << _EOF_
FROM excelfore/androidsdk-33:17-latest
ENV BUILD_NUMBER=$BUILD_NUMBER
RUN groupadd -f -g $gid -o lawnboy
RUN useradd -b /home -g $gid -m -o -u $uid lawnboy
USER lawnboy
WORKDIR /workspace
# ENTRYPOINT ./build-scripts/build_and_package.sh
ENTRYPOINT /bin/sh
_EOF_

echo "Using docker file: $DOCKER_FILE"
docker build -f "$DOCKER_FILE" -t "$DOCKER_IMAGE_NAME" "$BUILD_DIR"
GRADLE_DIR="$HOME"/.cache/excelfore.gradle."$JOB_NAME"
mkdir -p "$GRADLE_DIR" "$HOME"/.m2
# we can't use shared .gradle directory because this causes conflicts
# if multiple bulds (any Gradle builds) are running, and we can't share a daemon
# because we are inside docker
docker run --rm -it --volume "$TOP_DIR"/:/workspace:Z --volume "$HOME"/.m2/:/home/<USER>/.m2:Z --volume "$GRADLE_DIR":/home/<USER>/.gradle:Z --volume "$LOCAL_PROP":/workspace/local.properties:Z --env-file "$ENV_FILE" "$DOCKER_IMAGE_NAME"

