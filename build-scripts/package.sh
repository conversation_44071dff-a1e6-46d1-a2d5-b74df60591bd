#!/bin/bash -e
# Short Discription : Script is used to relese the eSync-Client pacakge for Android board.

THIS_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TOP_DIR="$(cd "$THIS_DIR"/../ && pwd)"
OUT="$TOP_DIR/build"
mkdir -p "$OUT"

BUILD_VERSION="$(cd "$TOP_DIR" && ./gradlew $GRADLE_OPS printVersion | grep ^VERSION= | awk -F= '{print $2}')"
test -z "$BUILD_VERSION" && {
    echo "Could not determine build version!"
    exit 1
}

( cd "$TOP_DIR" && ./gradlew --stacktrace -i $GRADLE_OPS dropSBom )
# $TODO - this doesn't work for some reason - mvnw can't find the Maven launcher
# ( cd "$TOP_DIR" && ./mvnw install )
# ( cd "$TOP_DIR" && ./mvnw com.excelfore:brp-thin-client-sbom-verify:sbom-verify )

echo "*** Client bundle files:"
ls -1 "$OUT"/esync-brp-release-"$BUILD_VERSION".*

