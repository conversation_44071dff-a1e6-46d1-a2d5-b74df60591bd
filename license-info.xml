<libraries>
    <library>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-javalite</artifactId>
        <licenses>
            <license>
                <url>https://opensource.org/licenses/BSD-3-Clause</url>
            </license>
        </licenses>
    </library>
    <library>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <licenses>
            <license>
                <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            </license>
        </licenses>
    </library>
    <library>
        <groupId>com.android.volley</groupId>
        <artifactId>volley</artifactId>
        <licenses>
            <license>
                <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            </license>
        </licenses>
    </library>
    <library>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <licenses>
            <license>
                <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            </license>
        </licenses>
    </library>
    <library>
        <!--
        For all intents and purposes, this is a part of Guava.
        It doesn't disclose any licenses. It can be used separately,
        but for us it's being pulled up by Guava.
        -->
        <groupId>com.google.guava</groupId>
        <artifactId>failureaccess</artifactId>
        <skip/>
    </library>
    <library>
        <groupId>org.jacoco</groupId>
        <artifactId>org.jacoco.agent</artifactId>
        <!-- it's not actually used in the dependency tree -->
        <skip/>
    </library>
</libraries>
