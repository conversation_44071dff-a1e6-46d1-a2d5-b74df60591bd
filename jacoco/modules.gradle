// Reference: 
// https://veskoiliev.com/how-to-setup-jacoco-for-android-project-with-java-kotlin-and-multiple-flavours/
// https://thsaravana.github.io/blog/jacoco-single-coverage-for-multi-module/

apply plugin: 'jacoco'

jacoco {
    toolVersion '0.8.7'
}

tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}

project.afterEvaluate {

    (android.hasProperty('applicationVariants') ? android.'applicationVariants' : android.'libraryVariants')
            .all { variant ->
                def variantName = variant.name
                def capVariantName = variantName.capitalize()
                def testTaskName = "test${capVariantName}UnitTest"
                tasks.create(name: "${testTaskName}Coverage", type: JacocoReport) {

                    group = "Reporting"
                    description = "Generate Jacoco coverage reports for the ${variantName} build."

                    def excludes = [
                            '**/R.class',
                            '**/R$*.class',
                            '**/BuildConfig.*',
                            '**/Manifest*.*',
                            '**/*Test*.*',
                            'android/**/*.*',
                            'androidx/**/*.*',
                            '**/*$ViewInjector*.*',
                            '**/*Dagger*.*',
                            '**/*MembersInjector*.*',
                            '**/*_Factory.*',
                            '**/*_Provide*Factory*.*',
                            '**/*_ViewBinding*.*',
                            '**/AutoValue_*.*',
                            '**/R2.class',
                            '**/R2$*.class',
                            '**/*Directions$*',
                            '**/*Directions.*',
                            '**/*Binding.*'
                    ]

                    def jClasses = "${project.buildDir}/intermediates/javac/${variantName}/classes"
                    def javaClasses = fileTree(dir: jClasses, excludes: excludes)
                    classDirectories.from = files([javaClasses])

                    def sourceDirs = ["${project.projectDir}/src/main/java"]
                    sourceDirectories.from = files(sourceDirs)

                    executionData.from = files(["${project.buildDir}/jacoco/${testTaskName}.exec"])
                }

                tasks.getByName(testTaskName).finalizedBy tasks.getByName("${testTaskName}Coverage")
            }
}
