package com.excelfore.esync.agent.bld.sbom

import com.github.packageurl.PackageURL
import com.github.packageurl.PackageURLBuilder
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import org.apache.commons.compress.archivers.tar.TarArchiveEntry
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream
import org.apache.maven.settings.Settings
import org.codehaus.groovy.runtime.EncodingGroovyMethods
import org.eclipse.aether.DefaultRepositorySystemSession
import org.eclipse.aether.artifact.Artifact
import org.eclipse.aether.artifact.DefaultArtifact
import org.eclipse.aether.repository.RemoteRepository
import org.eclipse.aether.resolution.ArtifactRequest
import org.eclipse.aether.resolution.ArtifactResult
import org.gradle.api.DefaultTask
import org.gradle.api.artifacts.repositories.MavenArtifactRepository
import org.gradle.api.logging.Logger
import org.gradle.api.tasks.*
import org.jboss.shrinkwrap.resolver.impl.maven.bootstrap.MavenRepositorySystem

import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.security.MessageDigest
import java.util.zip.GZIPOutputStream

class SBomber extends DefaultTask {

    @Internal
    final Logger log = getLogger()

    @InputFile
    File depJsonFile

    @Internal
    File sBomFile

    @OutputDirectory
    File pkgDir

    @InputDirectory
    File topDir

    @Input
    String version

    /**
     * SBom we are to generate.
     */
    @Internal
    SBom sBom

    /**
     * Loaded dependencies from {@link #depJsonFile}.
     */
    @Internal
    Object lDep

    @Internal
    final dirList = new HashSet<String>()
    @Internal
    final depCache = [:]

    @Internal
    MavenRepositorySystem mvnSystem
    @Internal
    DefaultRepositorySystemSession mvnSession
    @Internal
    Map<String, RemoteRepository> remotes = [:]

    @TaskAction
    void dropSBom() {

        log.info("Dropping SBom")
        log.info("Loading dependencies from ${depJsonFile}")

        lDep = new JsonSlurper().parse(depJsonFile)
        log.info("Found ${lDep.size()} dependencies")

        sBom = new SBom(this)

        def app = project.project("app")
        def android = app.getExtensions().getByName("android")

        initResolver()

        /*
        android.applicationVariants.all {
            v -> log.info "* " + v.flavorName + ':' + v.buildType.name
        }
        */

        log.info("Packaging to ${pkgDir}")
        Files.createDirectories(this.pkgDir.toPath())
        Files.createDirectories(sBomFile.getParentFile().toPath())

        String pkgRoot = "esync-brp-release-${version}"
        sBomFile = new File(this.pkgDir, "${pkgRoot}.sbom.json")
        File pkgFile = new File(this.pkgDir, "${pkgRoot}.tar.gz")

        log.info("Package file ${pkgFile}")

        def pkgDir = "${topDir}/release_files"
        def outDirEsync = "${pkgRoot}/brp-esync"
        def eSyncAPK="${topDir}/app/build/outputs/apk"

        new FileOutputStream(pkgFile).withCloseable {fos->
            new GZIPOutputStream(fos).withCloseable {gz->
                new TarArchiveOutputStream(gz).withCloseable { tar->

                    addFile("${pkgDir}/README", "${pkgRoot}/", tar)
                    // $TODO: put back
                    // addFile("${topDir}/app/build/reports/dependency-check-report.html", "${pkgRoot}/vulnerabilities-report.html", tar)
                    addFile("${pkgDir}/permissions_com.excelfore.esync.brp.xml", "${pkgRoot}/", tar)

                    android.applicationVariants.all { variant ->

                        def bt = variant.buildType.name
                        def flavor = variant.flavorName

                        log.info("Handling build type ${bt}, flavor ${flavor}")

                        // $TODO - for now, we include debug variant only, because it's
                        //   signed by the debug keys. Release is not signed at all.
                        if (bt != 'debug') {
                            log.info("Skipping, build type not needed")
                            return
                        }
                        if (flavor != 'v1') {
                            log.info("Skipping, flavor not needed")
                            return
                        }
                        addFile("${eSyncAPK}/${flavor}/${bt}/brp-esync.apk", "${outDirEsync}/", tar)

                    }

                }
            }
        }

        makeHash(pkgFile, sBom.metadata.component)

        log.info("Writing to ${sBomFile}")

        new FileWriter(sBomFile).withCloseable {fw ->
            new PrintWriter(fw).withCloseable { pw ->
                pw.println(JsonOutput.prettyPrint(JsonOutput.toJson(sBom)))
                pw.flush()
            }
        }

    }

    def initResolver() {

        def ms = new Settings()
        // $TODO - is there a better way?
        ms.setLocalRepository("${System.getProperty("user.home")}/.m2/repository")

        project.getRepositories().findAll {
            if (it instanceof MavenArtifactRepository) {
                // Filter
                log.info("Adding repository ${it.name} @ ${it.getUrl()}")
                remotes.put(it.name, new RemoteRepository.Builder(it.name, "default", it.getUrl().toString()).build())
            } else {
                log.info("Skipping repository ${it.name} of ${it.getClass().name}")
            }
        }

        mvnSystem = new MavenRepositorySystem()
        mvnSession = mvnSystem.getSession(ms, false)

    }

    static def makeHash(File f, Object target) {

        target.hashes = [
            [
                alg: "SHA-256",
                content: sha256File(f)
            ]
        ]

    }

    static def sha256File(String f) {
        return sha256File(new File(f))
    }

    static def sha256File(File f) {

        new FileInputStream(f).withCloseable {
            def buf = new byte[128000]
            def md = MessageDigest.getInstance("SHA-256")
            while (true) {
                int nr = it.read(buf)
                if (nr < 0) { break }
                md.update(buf, 0, nr)
            }
            return encodeHex(md.digest())
        }

    }

    static String encodeHex(byte [] b) {

        return new StringWriter().withCloseable {
            EncodingGroovyMethods.encodeHex(b).writeTo(it)
            return it.toString()
        }

    }

    static def cleanPath(String s) {

        def p = new ArrayList<>(Arrays.asList(s.split("/")))
        p.removeIf({e->
            return e == "."
        })
        return p.join("/")

    }

    def addFile(String src, String dst, def tar) {

        File target = new File(dst)
        if (dst.endsWith("/")) {
            target = new File(target, new File(src).getName())
        }

        def parentDirs = []
        File parent = target.getParentFile()
        while (parent != null) {
            parentDirs.add(parent.toString())
            parent = parent.getParentFile()
        }
        parentDirs.reverse().each {
            if (!dirList.contains(it)) {
                addDir(tar, it)
                dirList.add(it)
            }
        }

        def srcFile = new File(src)
        if (!srcFile.exists()) {
            throw new IllegalArgumentException("${src} not found")
        }

        TarArchiveEntry ten = new TarArchiveEntry(srcFile, cleanPath(target.getPath()))
        tar.putArchiveEntry(ten)
        new FileInputStream(new File(src)).withCloseable { inp->
            byte [] buf = new byte[128000]
            while (true) {
                int nr = inp.read(buf)
                if (nr < 0) { break }
                tar.write(buf, 0, nr)
            }
        }
        tar.closeArchiveEntry()

        registerFile(src, target.getPath())

    }

    def excelforeComponent() {

        return [
            author: "Excelfore",
            group: "com.excelfore",
            version: version,
        ]

    }

    static def urlE(String s) {
        return URLEncoder.encode(s, StandardCharsets.UTF_8)
    }

    /**
     * Translate file into an SBom component entry
     * @param name name of the file to process
     */
    def registerFile(String file, String name) {

        def component = registerFileComponent(file, name)

        // should have bom-ref for cross-referencing
        if (component.purl == null) {
            component."bom-ref" = "urn:excelfore:file:${urlE(name)}"
        } else {
            bomRef(component)
        }

        // make sure to make the delivery itself depend on the component
        def mainRef = sBom.metadata.component."bom-ref"
        def dep = sBom.dependencies.find { it.ref == mainRef }
        if (dep == null) {
            dep = [
                    ref: mainRef,
                    dependsOn: []
            ]
            sBom.dependencies.add(dep)
        }

        dep.dependsOn.add(component."bom-ref")

    }

    def registerFileComponent(String file, String name) {

        String tName = new File(name).getName()

        def component = excelforeComponent()
        component.scope = "required"
        component.name = tName

        sBom.components.add(component)
        makeHash(new File(file), component)

        if (tName == "README") {
            component.description = "Overview and installation information"
            component.type = "file"
            component.scope = "optional"
            return component
        }

        if (tName == "vulnerabilities-report.html") {
            component.description = "Vulnerability scan report on client dependencies"
            component.type = "file"
            component.scope = "optional"
            return component
        }

        if (tName == "permissions_com.excelfore.esync.brp.xml") {
            component.description = "Additional system permissions declaration"
            component.type = "file"
            component.scope = "optional"
            return component
        }

        if (tName == "brp-esync.apk") {

            def v = new File(name).getParentFile().getName()
            def ov = v
            def d = "Thin client APK"

            component.description = d
            component.type = "application"
            component.name = "${ov}/${tName}"
            component.purl = "pkg:com.excelfore.esync.brp/brp/${urlE(ov)}@${urlE(version)}"
            bomRef(component)

            // $TODO : this is broken right now, because we can't use
            // our OSS license plugin that added the file locations to the JSON
            // attachDependencies(component)

            return component

        }

        throw new IllegalArgumentException("Don't know how to handle ${file} -> ${name} (${tName})")

    }

    def bomRef(Object component) {

        if (component.purl == null) {
            throw new IllegalArgumentException("Component has no PURL: "+component)
        }
        component."bom-ref" = makeBomRef(component.purl)

    }

    static def makeBomRef(String url) {

        def purl = new PackageURL(url)
        PackageURLBuilder b = PackageURLBuilder.aPackageURL()
        b.withType(purl.getType())
        .withNamespace(purl.getNamespace())
        .withName(purl.getName())
        .withVersion(purl.getVersion())
        .withSubpath(purl.getSubpath())

        return b.build().toString()
        
    }

    def attachDependencies(Object component) {

        def list = []

        lDep.each { dep->

            def ext = {
                def dot = dep.fileLocation.lastIndexOf(".")
                return dep.fileLocation.substring(dot+1)
            }()
            def coordinate = "${dep.group}:${dep.name}:${ext}:${dep.version}"

            def res = depCache.computeIfAbsent(coordinate, { key ->

                log.info("Resolving ${coordinate}")

                Artifact a = new DefaultArtifact(coordinate)
                def ar = new ArtifactRequest(a, new ArrayList<>(remotes.values()), null)
                ArtifactResult res = mvnSystem.resolveArtifact(mvnSession, ar)
                def id = res.getRepository().id
                def url = remotes.get(id).getUrl()
                while (url.endsWith("/")) {
                    url = url.substring(0, url.length() - 1)
                }
                log.info("${coordinate} came from ${id} @ ${url}")

                def pUrl = "pkg:maven/${urlE(dep.group)}/${urlE(dep.name)}@${urlE(dep.version)}?type=${urlE(ext)}"
                if (url != "https://repo.maven.apache.org/maven2") {
                    pUrl += "&repository_url=${urlE(url)}"
                }

                def comp = [
                    // $TODO: we can get more package information, but it's not particularly
                    // helpful. Not doing this unless the customer asks for it explicitly
                    group: dep.group,
                    name: dep.name,
                    version: dep.version,
                    purl: pUrl,
                    type: "library",
                    scope: "required"
                ]
                bomRef(comp)
                makeHash(new File(dep.fileLocation), comp)
                sBom.components.add(comp)

                return Objects.requireNonNull(comp."bom-ref")

            })

            list.add(res)

        }

        sBom.dependencies.add([
            ref: Objects.requireNonNull(component."bom-ref"),
            dependsOn: list
        ])


    }


    def addDir(def tar, def path) {

        path = cleanPath(path)

        if (!path.endsWith("/")) {
            path = path + "/"
        }

        def ten = new TarArchiveEntry(topDir, path)

        tar.putArchiveEntry(ten)
        tar.closeArchiveEntry()

    }

}