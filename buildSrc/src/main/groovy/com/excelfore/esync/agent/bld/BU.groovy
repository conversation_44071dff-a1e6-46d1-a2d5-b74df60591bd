package com.excelfore.esync.agent.bld

class BU {

    static def capitalize(s) {
        if (s == null) { return null }
        return String.valueOf(Character.toUpperCase(s.charAt(0))) + s.substring(1)
    }

    static def escapeStr(val) {
        def sb = new StringBuilder('"')
        val.each { c -> if (c == '"' || c == '\\') { sb.append('\\') }; sb.append(c) }
        sb.append('"')
        return sb.toString()
    }

}


