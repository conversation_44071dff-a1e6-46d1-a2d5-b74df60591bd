package com.excelfore.esync.agent.bld.sbom

import com.excelfore.esync.agent.bld.sbom.util.RFC3339

class SBom {

  String $schema = "http://cyclonedx.org/schema/bom-1.5.schema.json"
  String bomFormat = "CycloneDX"
  String specVersion = "1.5"
  String serialNumber = "urn:uuid:" + UUID.randomUUID()
  int version = 1
  List components = []
  List dependencies = []

  Map metadata

  SBom(SBomber dataSource) {

    metadata = [
        timestamp:
            RFC3339.now().toString(),
        tools:
            [
                [
                    vendor: "Excelfore",
                    name: "<PERSON><PERSON><PERSON><PERSON>",
                    version: dataSource.version
                ]
            ]
    ]

    def c = metadata.component = dataSource.excelforeComponent()
    c.name = "esync-brp-release-${dataSource.version}.tar.gz"
    c.description = "Excelfore eSync OTA thin client for BRP HU devices - distribution package"
    c."mime-type" = "application/gzip"
    c."bom-ref" = "urn:excelfore:file:${c.name}"
    c.type = "application"

  }

}
