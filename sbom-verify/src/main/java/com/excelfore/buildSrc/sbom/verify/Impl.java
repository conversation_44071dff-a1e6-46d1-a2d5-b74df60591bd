package com.excelfore.buildSrc.sbom.verify;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugin.logging.Log;
import org.apache.maven.plugin.logging.SystemStreamLog;
import org.apache.maven.plugin.AbstractMojo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.networknt.schema.InputFormat;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.PathType;
import com.networknt.schema.SchemaLocation;
import com.networknt.schema.SchemaValidatorsConfig;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;

@Mojo(name = "sbom-verify", requiresProject = true)
public class Impl extends AbstractMojo {

    @Parameter(readonly = false)
    private File sBomFile;

    @Parameter
    private boolean skip;

    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {

        if (skip) { return; }

        try {
            executeIt();
        } catch (MojoFailureException e) {
            throw e;
        } catch (Throwable e) {
            throw new MojoExecutionException(e);
        }

    }

    private void executeIt() throws Exception {

        Log log = getLog();

        JsonSchemaFactory jsonSchemaFactory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012, builder ->
                // This creates a mapping from $id which starts with https://www.example.org/ to the retrieval URI classpath:schema/
                builder.schemaMappers(schemaMappers -> schemaMappers.mapPrefix("http://cyclonedx.org/schema/", "classpath:/schema/"))
        );

        SchemaValidatorsConfig config = new SchemaValidatorsConfig();
        config.setPathType(PathType.JSON_POINTER);

        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode;
        try (InputStream is = new FileInputStream(sBomFile)) {
            jsonNode = mapper.readTree(is);
        }

        JsonSchema schema = jsonSchemaFactory.getSchema(SchemaLocation.of(jsonNode.get("$schema").asText()), config);

        Set<ValidationMessage> assertions = schema.validate(jsonNode, executionContext -> {
            // By default, since Draft 2019-09 the format keyword only generates annotations and not assertions
            executionContext.getExecutionConfig().setFormatAssertionsEnabled(true);
        });

        if (assertions.isEmpty()) {
            log.info("The BOM is valid");
            return;
        }

        log.error("The BOM is not valid");
        for (ValidationMessage vm : assertions) {
            // $TODO: maybe extract more info from the assertion
            log.error(vm.toString());
        }

        throw new MojoFailureException("The BOM is not valid, see earlier error for details");

    }


}

