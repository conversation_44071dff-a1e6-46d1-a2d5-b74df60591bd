package com.excelfore.esync.brp.test;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.Reporter;
import com.excelfore.esync.brp.model.sync_js.WInstallationRecord;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * Helper to manually cycle the reporter
 */
public class  ManualReportControl implements Constants {

    private final long timeoutMs;
    private final Semaphore latch = new Semaphore(0);
    // we are using mutable objects because we can't store
    // nulls in the queue, because queue.poll() returns null
    // on no elements available as well.
    private final BlockingQueue<ReporterCycleData> data = new ArrayBlockingQueue<>(1);

    /**
     * Create new control.
     * @param timeoutMs timeout for waiting, in general, to avoid infinite waits. Blowing through
     * the timeout will cause failures.
     */
    public ManualReportControl(long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }

    /**
     * Called by the reporter to cycle.
     * The call will block, and wait until it's released using {@link #advance()}
     * The scheduling lock is released while the reporter is waiting inside this method.
     * Once this method exits, reporter will not actually wait for scheduling interrupt,
     * and will go into the sending cycle.
     * @param wait wait duration that the reporter was planning to sleep on.
     */
    public void nextCycle(Long wait, List<WInstallationRecord> toSend) {

        L.e(TAG, "Pausing reporter for next cycle");

        boolean ok = LibUtil.reThrow(()->data.offer(new ReporterCycleData(wait, toSend), timeoutMs, TimeUnit.MILLISECONDS));

        if (ok) {

            ok = LibUtil.reThrow(()->latch.tryAcquire(timeoutMs, TimeUnit.MILLISECONDS));

        }

        L.i(TAG, "Reporter released");

        if (!ok) {
            // reporter couldn't wait for next cycle activation within the timeout.
            // most likely because the test is no longer controlling the queue
            throw new RuntimeException("Nobody at the wheel");
        }

    }

    public void advance() {

        L.i(TAG, "Releasing reporter");

        if (!data.isEmpty()) {
            data.clear();
            L.w(TAG, "you didn't call waitForCycle?");
        }
        latch.release();
    }

    /**
     * Wait for the reporter to reach the end of the cycle. Reporter will either
     * attempt to send data, or to sleep. When this method returns, it's guaranteed that the
     * reporter is stopped, and is waiting for {@link #advance()} to be called.
     * @return data available to the reporter at the end of the cycle.
     * @throws NoSuchElementException if the reporter didn't reach the end of the
     * cycle within the timeout.
     */
    @NotNull
    public ReporterCycleData waitForCycle() {

        ReporterCycleData val = LibUtil.reThrow(()->data.poll(timeoutMs, TimeUnit.MILLISECONDS));
        if (val == null) {
            throw new NoSuchElementException();
        }

        return val;

    }

    /**
     * Ensures all reports from the reporting queue have been drained.
     * Note that it resets the reporter request rate limit using {@link Reporter#resetRate()} to
     * ensure that data can be sent out.
     * @param r reporter instance
     * @return last reporting cycle data
     */
    public ReporterCycleData drain(Reporter r) {

        while (true) {

            ReporterCycleData rcd = waitForCycle();
            if (r.listOutstandingMessages().isEmpty()) { return rcd; }
            r.resetRate();
            advance();

        }

    }
}
