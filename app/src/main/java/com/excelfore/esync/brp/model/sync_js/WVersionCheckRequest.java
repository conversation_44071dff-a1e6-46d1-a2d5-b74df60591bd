package com.excelfore.esync.brp.model.sync_js;

import java.util.Collection;

public class WVersionCheckRequest {

    private Collection<WUpdatableEndpointInfo> current;
    private boolean reportedOnly;

    public Collection<WUpdatableEndpointInfo> getCurrent() {
        return current;
    }

    public WVersionCheckRequest setCurrent(Collection<WUpdatableEndpointInfo> current) {
        this.current = current;
        return this;
    }

    public boolean isReportedOnly() {
        return reportedOnly;
    }

    public WVersionCheckRequest setReportedOnly(boolean reportedOnly) {
        this.reportedOnly = reportedOnly;
        return this;
    }

}
