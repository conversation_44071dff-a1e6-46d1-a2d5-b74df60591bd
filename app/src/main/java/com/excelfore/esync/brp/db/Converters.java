package com.excelfore.esync.brp.db;

import androidx.room.TypeConverter;

import com.excelfore.esync.brp.util.JsonThings;


public class Converters {

    @TypeConverter
    public static String fromSettings(Settings s) {
        return toJson(s);
    }

    @TypeConverter
    public static Settings toSettings(String s) {
        return JsonThings.gsonNE().fromJson(s, Settings.class);
    }

    private static String toJson(Object o) {
        return JsonThings.gsonNE().toJson(o);
    }

}
