package com.excelfore.esync.brp.test;

import com.excelfore.esync.brp.Constants;

import java.nio.file.Path;
import java.util.List;
import java.util.concurrent.Executor;

public interface Configurator {

    default int getMaxVerificationErrors() {
        return Constants.MAX_VERIFICATION_ERRORS;
    }

    /**
     * Invoked by LogCatReader to read the "campaign failure" logs - a finite
     * log file.
     * @param command original log cat command invoked by log cat reader
     * @return file path to read the fake logs from
     */
    default Path getFiniteLog(List<String> command) { return null; }

    /**
     * Invoked by LogCatReader to read the "live" logs - an infinite log source,
     * a local host port number that can be connected to to retrieve the
     * buffered logs, alongside with any additionally published logs.
     * @param command original log cat command invoked by log cat reader
     * @return port number to use to for a TCP connection to read log data.
     */
    default Integer getRunningLog(List<String> command) { return null; }

    /**
     * Executor to use for both rooms queries and transactions.
     * @return executor to use, or {@code null} to not use any.
     */
    default Executor getRoomsExecutor() { return null; }

}
