package com.excelfore.esync.brp.session;

import com.excelfore.esync.brp.model.sync_js.ClientMessage;

public abstract class OperationResult {

    // indicates whether the operation itself failed
    private boolean operationFailed;

    // indicates whether the entire update should be
    // considered as failed as a result
    private boolean updateFailed;

    // present if operation failed with an exception
    private Throwable exception;

    // message to set to the report as a result of this operation.
    private ClientMessage message;

    private boolean terminalFailure;

    protected OperationResult() {}

    public void transientFailure(ClientMessage message, Throwable exception) {
        operationFailed = true;
        this.message = message;
        this.exception = exception;
    }

    public void updateFailure(ClientMessage message, Throwable exception) {
        operationFailed = true;
        updateFailed = true;
        this.message = message;
        this.exception = exception;
    }

    public void success(ClientMessage message) {
        this.message = message;
    }

    public abstract boolean isDownload();

    public boolean isOperationFailed() {
        return operationFailed;
    }

    public boolean isUpdateFailed() {
        return updateFailed;
    }

    public Throwable getException() {
        return exception;
    }

    public ClientMessage getMessage() {
        return message;
    }

    public boolean isTerminal() { return terminalFailure; }

    public void setTerminal() { this.terminalFailure = true; }

}
