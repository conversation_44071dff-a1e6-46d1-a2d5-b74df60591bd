package com.excelfore.esync.brp.net;

import com.android.volley.Request;
import com.android.volley.Response;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.SessionWork;

import java.util.function.Function;

public class RequestInfo<T> implements AuthHeaderContract {

    public final BRPUpdateApp app;
    public final int method;
    public final String url;
    public final Object body;
    public final Response.Listener<T> listener;
    public final Response.ErrorListener errorListener;
    public final Function<RequestInfo<T>, Request<T>> requestMaker;
    public final boolean isReport;
    public final RequestRunState rrs;
    public final SessionWork task;

    private boolean retryingAuth;
    private boolean mbbNoSource;

    @Override
    public boolean isForceAuth() {
        return retryingAuth;
    }

    @Override
    public void tokenNoSource() {
        mbbNoSource = true;
    }

    public RequestInfo(BRPUpdateApp app,
                       SessionWork task,
                       int method, String url, Object body,
                       RequestRunState rrs,
                       Response.Listener<T> listener,
                       Response.ErrorListener errorListener,
                       Function<RequestInfo<T>, Request<T>> requestMaker,
                       boolean isReport) {

        this.app = app;
        this.task = task;
        this.method = method;
        this.url = url;
        this.body = body;
        this.rrs = rrs;
        this.listener = d->{
            rrs.ackFinished(RequestFinishState.FULL_COUNT);
            listener.onResponse(d);
        };
        this.errorListener = NetUtils.makeVolleyErrorListener(this, errorListener);
        this.requestMaker = requestMaker;
        this.isReport = isReport;

    }

    public boolean isAuthRetry() {
        return retryingAuth;
    }

    public void retryAuth() {
        retryingAuth = true;
    }

    public boolean isMbbNoSource() {
        return mbbNoSource;
    }
}
