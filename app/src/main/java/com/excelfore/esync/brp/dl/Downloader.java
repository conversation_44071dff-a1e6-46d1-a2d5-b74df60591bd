package com.excelfore.esync.brp.dl;

import android.os.SystemClock;
import android.util.Base64;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.R;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.SessionWork;
import com.excelfore.esync.brp.db.Settings;
import com.excelfore.esync.brp.db.UpdateProgress;
import com.excelfore.esync.brp.db.UpdateProgress.DownloadFileState;
import com.excelfore.esync.brp.db.UpdateState;
import com.excelfore.esync.brp.model.sync_js.ClientMessage;
import com.excelfore.esync.brp.model.sync_js.ComponentState;
import com.excelfore.esync.brp.model.sync_js.WComponentVersion;
import com.excelfore.esync.brp.model.sync_js.WDownload;
import com.excelfore.esync.brp.model.sync_js.WInstallation;
import com.excelfore.esync.brp.session.CurrentSession;
import com.excelfore.esync.brp.session.SessionRequest;
import com.excelfore.esync.brp.test.TL;
import com.excelfore.esync.brp.util.CFormat;
import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.RunnableT;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.time.Duration;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class Downloader extends SessionWork implements Constants {

    final File downloadFile;
    private final String downloadFileName;
    final File updateFile;
    final WInstallation target;

    long uiReported;

    public Downloader(BRPUpdateApp app, CurrentSession session) {
        super(app, session);
        target = fromUpdateState(UpdateState::getTarget);
        if (target == null) {
            throw new NullPointerException("Downloader invoked with null target");
        }
        String version = target.getTarget().getVersion();
        downloadFile = getDownloadFile(version, true);
        downloadFileName = getDownloadFileName(version, true);
        updateFile = getDownloadFile(version, false);
    }

    public DownloadResult download() {

        DownloadResult result = new DownloadResult();

        try {

            WComponentVersion target = this.target.getTarget();

            // result needs decrypted file
            result.setDownloadedFile(updateFile);

            L.d(Constants.TAG, "download() invoked");

            if (fromProgress(UpdateProgress::isDownloaded)) {
                checkDownload(false, result);
                if (result.isOperationFailed()) {
                    onProgress(p->p.setDownloaded(false));
                    app.getScheduler().scheduleNow(new SessionRequest("Previously downloaded update verification failed"));
                }
                return result;
            }

            if (fromUpdateState(us->{
                UpdateProgress up = us.getProgress();
                int maxVerifyError = TL.config.getMaxVerificationErrors();
                if (up.getVerificationFailures() >= maxVerifyError) {
                    result.updateFailure(ClientMessage.F.VERIFICATION_MAX_REACHED.make(up.getVerificationFailures(), maxVerifyError, target.getVersion(), up.getVerificationError()), null);
                    result.setTerminal();
                    return true;
                }
                return false;
            })) {
                return result;
            }

            OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

            Settings cfg = app.getSettings();

            Request.Builder requestBuilder = new Request.Builder();

            // Check if download URL is from our custom domain that requires custom certificates
            String downloadUrl = target.getDownload().getUrl();
            boolean isCustomDomain = downloadUrl != null && downloadUrl.contains("brp-esync-dev.esyncsdk.com");

            if (!cfg.isDownloadIsPublicURL() || isCustomDomain) {
                if (isCustomDomain) {
                    L.i(TAG, "Download URL is from custom domain, applying custom trust manager/authentication");
                } else {
                    L.i(TAG, "Download URL is private, applying custom trust manager/authentication");
                }
                clientBuilder.sslSocketFactory(app.getSSLSocketFactory(), app.getTrustManager());
                for (Map.Entry<String, String> me : app.makeExtraHeaders(()->false).entrySet()) {
                    requestBuilder.header(me.getKey(), me.getValue());
                }
            } else {
                L.i(TAG, "Download URL is public, using public trust store");
                // clientBuilder.sslSocketFactory(app.getSSLSocketFactoryPublic(), app.getTrustManagerPublic());
            }

            String useUrl = target.getDownload().getUrl();

            // DEVOPS-2761 workaround - CDN wasn't configured with https

            L.i(TAG, "Original download url: "+useUrl);

            var origUrl = HttpUrl.get(useUrl);
            var ub = origUrl.newBuilder();
            ub.scheme("https");
            var oHost = origUrl.host();
            if (oHost.startsWith("dkzplygxw7157")) {
                ub.host("cdn.esyncbrp.xyz");
            }

            useUrl = ub.build().toString();

            requestBuilder.url(useUrl);
            result.setUrl(useUrl);

            cleanDownloadDirectory();

            L.i(TAG, "Will download to " + downloadFile.getAbsolutePath() +
                    ", from " + useUrl);

            String eTag = fromProgress(UpdateProgress::getETag);

            if (!downloadFile.exists()) {
                // If a download file is deleted while downloading,
                // notifying failure (only if it hasn't been notified yet).
                if (fromProgress(UpdateProgress::getDownloadFileState) == DownloadFileState.CREATED) {
                    onProgress(p->p.setDownloadFileState(DownloadFileState.NOT_FOUND));
                    // Run session immediately after notifying this failure
                    app.getScheduler().scheduleNow(new SessionRequest("Download file not found"));
                    return handleDownloadFailedError(result,
                            new DownloadStoreException(
                                    new FileNotFoundException("Download file not found")));
                }

                L.i(TAG, "Download file not found, resetting download state");
                onProgress(p->p.setETag(null));
            }

            long bytesDownloaded = 0;

            if (eTag == null) {
                L.i(TAG, "Download continuation is not possible, no saved ETag found");
            }

            if (eTag != null) {
                bytesDownloaded = downloadFile.exists() ? downloadFile.length() : 0;
                if (bytesDownloaded == 0) {
                    L.i(TAG, "Download file is empty, no continuation possible");
                    eTag = null;
                }
            }

            boolean downloadNeeded = true;

            if (eTag != null) {
                long downloadSize = fromProgress(UpdateProgress::getDownloadSize);
                if (downloadSize != 0 && downloadSize == bytesDownloaded) {
                    L.i(TAG, "Downloaded size " + bytesDownloaded +
                            " matches latest known download length, skipping download");
                    downloadNeeded = false;
                } else {
                    L.i(TAG, "Attempting to download from " + bytesDownloaded);
                    requestBuilder.header("range", "bytes=" + bytesDownloaded + "-");
                    requestBuilder.header("if-range", eTag);
                }
            }

            if (downloadNeeded) {

                app.getMainActivity().showProgress();

                long downloadedMb = bytesDownloaded / Constants.BYTES_IN_MEBIBYTE;
                if (bytesDownloaded % Constants.BYTES_IN_MEBIBYTE > 0) {
                    downloadedMb++;
                }

                Duration connectTimeout = cfg.getDlConnectTimeout();
                Duration readTimeout = cfg.getDlReadTimeout().plus(cfg.getDlSkipTimeoutMS().multipliedBy(downloadedMb));

                clientBuilder.readTimeout(readTimeout);
                clientBuilder.connectTimeout(connectTimeout);

                L.i(TAG, "Using download timeouts : connect=" + connectTimeout.toMillis() + ", read=" + readTimeout.toMillis());

                OkHttpClient client = clientBuilder.build();
                app.registerOkResources(client);

                requestBuilder.get();

                Request request = requestBuilder.build();

                report(r->r.setComponentState(ComponentState.DOWNLOAD_PROGRESSING));

                try (RandomAccessFile raf = new RandomAccessFile(downloadFile, "rwd")) {

                    if (eTag == null) {
                        L.d(TAG, "Truncating downloaded file to 0, since performing full download");
                        raf.setLength(0);
                    }

                    try (Response response = client.newCall(request).execute()) {

                        int httpCode = response.code();

                        response.headers().forEach(h-> L.i(TAG, "DL Header "+h));

                        ContentRangeHeader crh = null;

                        if (httpCode == 200) {
                            // we are getting full payload.
                            crh = acceptHeadersAndGetContentLength(response, true);
                            L.d(TAG, "Downloading full binary");
                        } else if (httpCode == 206) {
                            crh = acceptHeadersAndGetContentLength(response, false);
                        } else if (httpCode == 416) {
                            L.e(TAG, "Received range error from the server, resetting known range");
                            onProgress(p->p.setETag(null));
                            result.transientFailure(ClientMessage.F.DOWNLOAD_HTTP_CODE.make(useUrl, httpCode), null);
                            app.getScheduler().scheduleNow(new SessionRequest("Restart to download full binary", false));
                        } else {
                            L.e(TAG, "Unsupported HTTP code " + httpCode + " during download");
                            result.transientFailure(ClientMessage.F.DOWNLOAD_HTTP_CODE.make(useUrl, httpCode), null);
                        }

                        if (crh != null) {
                            result.setCrh(crh);
                            downloadIO(response.body(), raf, crh, result);
                        }

                    } catch (DownloadStoreException e) {
                        return handleDownloadFailedError(result, e);
                    } catch (CampaignAbortedException e) {
                        return handleCampaignAborted(result, e);
                    } catch (Exception e) {
                        // anything that comes out of this block ought to be download-related.
                        return handleDownloadNetworkError(result, e);
                    }

                }

            }

            if (!result.isOperationFailed()) {

                checkDownload(true, result);

                if (!result.isOperationFailed()) {
                    onProgress(p->{
                        p.setETag(null);
                        p.setDownloaded(true);
                    });
                    
                    if (!downloadFile.delete()) {
                        L.e(TAG, "Failed to delete originally downloaded file " +
                                downloadFile.getAbsolutePath(), new Exception());
                    } else {
                        onProgress(p->p.setDownloadFileState(DownloadFileState.DELETED_ON_COMPLETE));
                    }
                } else {
                    // Discovered during testing. If download verification fails,
                    // it's prudent to request a session right away, either to re-attempt the
                    // download, or to force terminal failure on verification count exhaustion.
                    app.getScheduler().scheduleNow(new SessionRequest("Download verification failed"));
                }

            }

        } catch (Exception e) {
            return handleDownloadFailedError(result, e);
        } finally {
            if (result.isRangeError()) {
                onProgress(p->p.setETag(null));
            }
            app.getMainActivity().hideProgress();
        }

        return result;

    }

    private ContentRangeHeader acceptHeadersAndGetContentLength(Response response, boolean fullContent) throws IOException {

        PTX tx = new PTX();

        try {

            String eTag = response.header("ETag");
            String pETag = fromProgress(UpdateProgress::getETag);

            if (!Objects.equals(eTag, pETag)) {
                tx.add(p->p.setETag(eTag));
                L.i(TAG, "Saving eTag value "+eTag+" from incoming headers");
            } else {
                L.i(TAG, "Incoming eTag "+eTag+" matches latest known eTag");
            }

            String hdrVal = Objects.requireNonNull(response.header("content-length"),
                    "No content-length header was provided");

            L.i(TAG, "Bytes to download: " + hdrVal);

            ContentRangeHeader crh;

            if (fullContent) {

                crh = new ContentRangeHeader("bytes */" + hdrVal);

            } else {

                String range = response.header("content-range");
                L.d(TAG, "Partial download: " + range);
                try {
                    crh = new ContentRangeHeader(range);
                } catch (Exception e) {
                    L.e(TAG, "Failed to parse incoming content range "+range, e);
                    L.e(TAG, "Forcing to restart download from scratch. Please file a bug report.");
                    tx.add(p->p.setETag(null));
                    throw new IOException("Can not parse range value "+range);
                }

            }

            tx.add(p-> p.setDownloadSize(crh.getTotal()));

            return crh;

        } finally {
            tx.execute();
        }


    }

    private DownloadResult handleDownloadNetworkError(DownloadResult result, Exception e) {
        L.e(TAG, "Download failed due to network", e);
        result.transientFailure(ClientMessage.F.DOWNLOAD_NETWORK_ERROR.make(result.getUrl(), e.getMessage() + result.downloadedDetails()), e);
        // set "suppressIfRunning" to true, otherwise it creates a resonance with
        // small session intervals. It's OK that if by the time we retry the download, the session
        // is already running, so it will attempt the download anyway.
        app.getScheduler().scheduleIn(Constants.RETRY_DOWNLOAD, true,
                new SessionRequest("Restart download after a network error"));
        return result;
    }

    private DownloadResult handleDownloadFailedError(DownloadResult result, Exception e) {
        L.e(TAG, "Download failed, non-network", e);
        result.updateFailure(ClientMessage.F.DOWNLOAD_STORE_ERR.make(e.getMessage() + result.downloadedDetails()), e);
        return result;
    }

    private DownloadResult handleCampaignAborted(DownloadResult result, Exception e) {
        L.e(TAG, "Download stopped, aborted installation record detected", e);
        result.updateFailure(ClientMessage.F.CAMPAIGN_ABORTED.make(), e);
        result.setTerminal();
        return result;
    }

    private void checkDownload(boolean decrypt, DownloadResult result) {

        if (target == null) {
            result.updateFailure(null, new Exception("No target object"));
            return;
        }

        try {

            WDownload dlInfo = target.getTarget().getDownload();
            boolean hashed = false;

            MessageDigest md = MessageDigest.getInstance("SHA-256");

            PKCS7Verifier verifier = new PKCS7Verifier(this);

            if (decrypt) {

                verifier.start();
                decrypt(dlInfo, md, result, verifier);
                hashed = true;

                if (result.isOperationFailed()) { return; }

            }

            BasicFileAttributes attrs = Files.readAttributes(updateFile.toPath(), BasicFileAttributes.class);
            long lmd = attrs.lastModifiedTime().toMillis();

            if (!hashed) {

                if (attrs.size() != dlInfo.getSize()) {
                    result.updateFailure(ClientMessage.F.FILE_SIZE_MISMATCH.make(updateFile.getAbsoluteFile(), attrs.size(), dlInfo.getSize()), null);
                    return;
                }

                if (lmd <= fromProgress(UpdateProgress::getVerifiedTimestamp)) {
                    L.d(TAG, "File " + updateFile.getAbsolutePath() + " has not changed since last verification");
                    return;
                }

                verifier.start();

                try (FileInputStream fis = new FileInputStream(updateFile)) {
                    DigestInputStream dis = new DigestInputStream(fis, md);
                    LibUtil.copyIO(dis, verifier.feeder);
                }
            }

            String calculated = Base64.encodeToString(md.digest(), Base64.NO_WRAP);
            String expected = target.getTarget().getDownload().getSha256();

            if (!Objects.equals(calculated, expected)) {
                result.updateFailure(ClientMessage.F.SHA_MISMATCH.make(downloadFile.getAbsoluteFile(), expected, calculated), null);
                // $TODO: we are existing before stopping the verifier, which may make
                // the session task reaper freak out.
                return;
            }

            if (verifier.isStarted()) {
                verifier.feeder.close();
                NegativeVerifierResult verifierResult = verifier.result.get();
                if (verifierResult != null) {
                    if (verifierResult.retryLater) {
                        // verifier may request a retry.
                        result.setCountNoVerificationFailure(true);
                    }
                    result.updateFailure(ClientMessage.F.VERIFYING_BINARY_INTEGRITY.make(verifierResult.message), null);
                    return;
                }

            }

            onProgress(p->p.setVerifiedTimestamp(lmd));

        } catch (Exception e) {
            result.updateFailure(ClientMessage.F.VERIFYING_BINARY_INTEGRITY.make(e.getMessage()), e);
            L.e(TAG, "Failed to verify binary " + updateFile.getAbsolutePath(), e);
        } finally {
            if (result.isOperationFailed()) {
                boolean fatalVerificationError = (decrypt && !result.isCountNoVerificationFailure()) || !downloadFile.exists();

                onProgress(up->{
                    if (fatalVerificationError) {
                        up.setVerificationFailures(up.getVerificationFailures() + 1);
                        L.e(TAG, "Verification of " + updateFile.getAbsolutePath() +
                                " failed, now have " + up.getVerificationFailures() +
                                " verification failures");
                    }

                    result.setVerificationError(true);

                    up.setDownloaded(false);
                    up.setVerificationError(result.getMessage().getString());
                });

                if (fatalVerificationError) {
                    if (!downloadFile.delete()) {
                        L.e(TAG, "Failed to delete " + downloadFile.getAbsolutePath());
                    }
                }
            }

        }

    }

    private void decrypt(WDownload dlInfo, MessageDigest hashInto,
                         DownloadResult result, PKCS7Verifier verifier) {

        try {
            long maxSize = dlInfo.getSize();
            CopyWithMax hash = new CopyWithMax(hashInto, verifier, maxSize);

            ConsumerT<RunnableT<Exception>, Exception> noVerify = op->{
                try {
                    op.run();
                } catch (Exception e) {
                    // if we encountered a "write" error, don't count it as a verification
                    // failure.
                    result.setCountNoVerificationFailure(true);
                    throw e;
                }
            };

            Decrypts<?> decrypts;
            String method = dlInfo.getMethod();
            if (method == null) {
                decrypts = new NoEncDecrypts(dlInfo, this, noVerify, hash);
            } else if ("AES/CTR/NoPadding".equals(method)) {
                decrypts = new CipherDecrypts(dlInfo, this, noVerify, hash);
            } else {
                throw new Exception("Unknown method "+method);
            }

            decrypts.run();

        } catch (Exception e) {
            result.updateFailure(ClientMessage.F.DECRYPTION_FAILED.make(e.getMessage()), e);
            L.e(TAG, "Decryption of " + downloadFile.getAbsolutePath()+" failed", e);
        }

    }


    private void downloadIO(ResponseBody body, RandomAccessFile raf,
                            ContentRangeHeader crh, DownloadResult result) throws Exception {

        if (raf.length() < crh.getFrom()) {
            result.transientFailure(null, new Exception("Downloaded file has "+
                    raf.length()+" bytes, but download starts from "+crh.getFrom()));
            result.setRangeError(true);
        }

        Objects.requireNonNull(body, "Null response body object");

        if (raf.length() > crh.getFrom()) {
            raf.setLength(crh.getFrom());
        }

        // crh.getTotal() is guaranteed to be >0 at this point
        int percent = (int) (crh.getFrom() * 100L / crh.getTotal());

        try {
            int $percent = percent;
            report(r->r.setDownloadPercent($percent));
        } catch (Exception e) {
            throw new DownloadStoreException(e);
        }

        onProgress(up->up.setDownloadStarted(System.currentTimeMillis()));

        byte[] buffer = new byte[10 * (int) BYTES_IN_MEBIBYTE];

        try (InputStream is = body.byteStream()) {

            long downloaded = crh.getFrom();
            long printedProgress = SystemClock.elapsedRealtime();

            RateTracker dl = new RateTracker(R.id.dlnSpeed);
            RateTracker wr = new RateTracker(R.id.dlwSpeed);

            while (true) {

                long beforeNS = SystemClock.elapsedRealtimeNanos();
                int nr = is.read(buffer);
                if (nr < 0) {
                    break;
                }

                try {

                    dl.recordNS(beforeNS, SystemClock.elapsedRealtimeNanos(), nr);

                    downloaded += nr;

                    result.setBytesDownloaded(result.getBytesDownloaded() + nr);

                    if (downloaded > crh.getTotal()) {
                        throw new RuntimeException("Downloaded " + downloaded +
                                " bytes, exceeding total declared length " + crh.getTotal());
                    }

                    // Detect file missing each time file is written.
                    // RandomAccessFile has limited open options and always creates
                    // a new file (if it does not exist), when opening for writing.
                    // So, I changed this to use Files.write() in appending mode.
                    // Download file will be opened for appending, then synchronized.
                    // If the file didn't exist, IOException is thrown.
                    beforeNS = SystemClock.elapsedRealtimeNanos();
                    Files.write(Paths.get(downloadFile.getAbsolutePath()), Arrays.copyOf(buffer, nr), 
                        StandardOpenOption.WRITE, StandardOpenOption.APPEND, StandardOpenOption.SYNC);
                    wr.recordNS(beforeNS, SystemClock.elapsedRealtimeNanos(), nr);

                    long now = SystemClock.elapsedRealtime();
                    if (now > printedProgress + Duration.ofMinutes(1).toMillis()) {
                        printedProgress = now;
                        L.v(TAG, "Downloaded "+downloaded+" bytes out of "+crh.getTotal());
                    }

                    int newPercent = (int) ((downloaded * 100L) / crh.getTotal());
                    if (newPercent > percent) {
                        if (newPercent % 10 == 0) {
                            report(r->r.setDownloadPercent(newPercent));
                        }
                        percent = newPercent;

                        // Change the state of the download file to CREATED
                        // since downloading is in progress.
                        if (fromProgress(UpdateProgress::getDownloadFileState) != DownloadFileState.CREATED) {
                            onProgress(p->p.setDownloadFileState(DownloadFileState.CREATED));
                        }
                    }

                    reportUIProgress(downloaded, crh.getTotal(), false, dl, wr);

                } catch (Exception e) {
                    if (!downloadFile.exists()) {
                        // Change state to NOT_FOUND to avoid repeatedly notifying of this error.
                        onProgress(p->p.setDownloadFileState(DownloadFileState.NOT_FOUND));
                    }
                    throw new DownloadStoreException(e);
                }

                /*
                $TODO removed this, as this generates an insane amount of noise,
                  opening/closing the DB every on read.

                // check aborting
                // Pending abort can be received via report response.
                WInstallation target = fromUpdateState(UpdateState::getTarget);
                if (target.isAborted()) {
                    throw new CampaignAbortedException("Aborted installation record is detected while downloading");
                }
                 */
            }

            // JCLT-1 Apparently, we may get no I/O error or anything, but not get the whole
            // file. Brr.
            long finalLen = downloadFile.length();
            if (finalLen < crh.getTotal()) {
                throw new IOException(new CFormat("Download library returned OK, but we only have %,d bytes in the file: %s")
                        .format(finalLen, result.downloadedDetails()));
            }

            report(r->{
                r.setDownloadPercent(100);
                r.setError(ClientMessage.F.DOWNLOADED.make(result.downloadedDetails()));
                r.setComponentState(ComponentState.DOWNLOAD_COMPLETE);
            });

            if (dl.bytes > 0) {
                var dln = new RateTracker(R.id.dlSpeed);
                onProgress(up->dln.recordMS(up.getDownloadStarted(), System.currentTimeMillis(), finalLen));
                dln.update(app.getMainActivity());
            }

        }

    }

    private static String getDownloadFileName(String version, boolean forDownload) {
        return LibUtil.reThrow(()->URLEncoder.encode(version, StandardCharsets.UTF_8) +
                (forDownload ? ".ct" : ".pt"));
    }

    private File getDownloadFile(String version, boolean forDownload) {
        return new File(app.getDownloadDir(), getDownloadFileName(version, forDownload));
    }

    private void cleanDownloadDirectory() {

        File downloadDir = new File(app.getDownloadDir());

        if (!downloadDir.exists()) {
            if (!downloadDir.mkdirs()) {
                L.e(Constants.TAG,"mkdirs() failed on "+downloadDir.getAbsolutePath());
            }
        }

        File [] files = downloadDir.listFiles(File::isFile);

        if (files == null) {
            L.i(Constants.TAG, downloadDir.getAbsolutePath()+" is empty");
            return;
        }

        for (File file : files) {
            if (!downloadFileName.equals(file.getName())) {
                L.i(Constants.TAG, "Removing obsolete download file "+file.getAbsolutePath());
                if (!file.delete()) {
                    L.e(Constants.TAG, "Failed to remove file "+file.getAbsolutePath());
                }
            }
        }

    }

    public void reportUIProgress(long progress, long total, boolean second, RateTracker...displays) {

        long now = System.currentTimeMillis();
        if (now < uiReported + 1000) {
            return;
        }

        uiReported = now;

        var rel = BigDecimal.valueOf(progress)
                .divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(0.5));
        if (second) {
            rel = rel.add(BigDecimal.valueOf(0.5));
        }

        // we have max granularity of 10000, so that's we're using
        var lr = rel.multiply(BigDecimal.valueOf(10000)).longValue();

        var ma = app.getMainActivity();

        ma.runOnUiThread(()->{
            ma.setProgress(lr);
            for (var rt : displays) {
                rt.update(ma);
            }
        });


    }

    static class DownloadStoreException extends Exception {
        DownloadStoreException(Exception e) {
            super(e.getMessage(), e);
        }
    }

    static class CampaignAbortedException extends Exception {
        CampaignAbortedException(String message) {
            super(message);
        }
    }


}
