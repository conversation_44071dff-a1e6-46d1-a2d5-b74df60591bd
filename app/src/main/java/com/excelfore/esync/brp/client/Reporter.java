package com.excelfore.esync.brp.client;

import static com.excelfore.esync.brp.Constants.PREF_REPORT_SEQUENCE;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;

import androidx.annotation.VisibleForTesting;

import com.android.volley.NetworkResponse;
import com.android.volley.ParseError;
import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.toolbox.HttpHeaderParser;
import com.android.volley.toolbox.JsonRequest;
import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.db.UpdateState;
import com.excelfore.esync.brp.model.sync_js.WInstallationRecord;
import com.excelfore.esync.brp.net.EmergencyRequestRunState;
import com.excelfore.esync.brp.net.Endpoints;
import com.excelfore.esync.brp.net.GlobalRequestLimiter;
import com.excelfore.esync.brp.net.NetUtils;
import com.excelfore.esync.brp.net.RequestDelayInfo;
import com.excelfore.esync.brp.net.RequestInfo;
import com.excelfore.esync.brp.net.RequestRunState;
import com.excelfore.esync.brp.test.ManualReportControl;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.MyUtil;
import com.excelfore.esync.brp.util.RFC3339;
import com.excelfore.esync.brp.util.RunnableT;
import com.squareup.tape2.ObjectQueue;
import com.squareup.tape2.QueueFile;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.TestOnly;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

public class Reporter extends Thread {

    private final BRPUpdateApp app;
    private final Object queueLock = new Object();
    private final Object reportLock = new Object();
    private transient ObjectQueue<WInstallationRecord> reportQueue;
    private RequestRunState rrs;
    private final GlobalRequestLimiter grl = new GlobalRequestLimiter();
    public boolean rateLimitDisabled;
    private final Object schedulingLock = new Object();
    private volatile boolean quit;
    private Long forceDelayMs;
    private ManualReportControl mrc;

    public Reporter(BRPUpdateApp app) {

        this.app = app;

        Runnable init = ()->{
            synchronized (queueLock) {
                // in case of re-initialization
                reportQueue = null;
            }
            // $TODO: hacked to let reports always go out right away
            // rrs = new LimitedRequestRunState(app, grl, Endpoints.REPORTS);
            rrs = new EmergencyRequestRunState();
            initQueue();
        };

        init.run();

        app.stateResetRunners.add(init::run);
        start();

    }

    private void initQueue() {
        initQueue(false);
    }

    private void initQueue(boolean lastResort) {

        File physFile = new File(app.getFilesDir(), Constants.REPORT_QUEUE_FILE);
        if (lastResort) {
            if (!physFile.delete()) {
                L.e(Constants.TAG, "Failed to remove queue file "+ physFile.getAbsolutePath());
            }
        }

        synchronized (queueLock) {
            try {
                QueueFile queueFile = new QueueFile.Builder(physFile).build();
                //noinspection NullableProblems
                reportQueue = ObjectQueue.create(queueFile, new ObjectQueue.Converter<>() {
                    @Override
                    public WInstallationRecord from(byte[] source) {
                        return JsonThings.gsonNE().fromJson(new String(source),
                                WInstallationRecord.class);
                    }

                    @Override
                    public void toStream(WInstallationRecord value, OutputStream sink) throws IOException {
                        sink.write(JsonThings.gsonNE().toJson(value).getBytes());
                    }
                });
            } catch (Exception e) {
                if (lastResort) {
                    throw new RuntimeException("Failed to initialize the reporting queue", e);
                } else {
                    L.wtf(Constants.TAG, "Failed to open the queue file"+physFile+
                            ", will have to eject the report queue", e);
                    initQueue(true);
                }
            }
        }
    }

    private void queueOp(RunnableT<IOException> r) {
        queueOp(()->{
            r.run();
            return null;
        });
    }

    private <T> T queueOp(Callable<T> r) {
        for (int i = 0; i < 3; i++) {
            try {
                return r.call();
            } catch (Exception e) {
                L.e(Constants.TAG, "Failed to perform a queue operation, reloading", e);
                initQueue();
            }
        }
        // ok, that's bad.
        L.wtf(Constants.TAG, "Failed to perform queue operation 3 times, ejecting queue");
        initQueue(true);
        try {
            return r.call();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SuppressLint("ApplySharedPref")
    public void report(UpdateState state) {

        try {

            synchronized (reportLock) {

                WInstallationRecord status = state.getReport();

                if (status.getId() == null) {
                    L.w(Constants.TAG, "Attempted report without ID - no initialized updates?");
                    return;
                }

                if (!status.isChanged()) {
                    return;
                }

                status.setStateSequence(state.getStateSequence());

                SharedPreferences sp = app.getSharedPreferences(Constants.SP_REPORT_SEQUENCE, Context.MODE_PRIVATE);
                long seq = sp.getLong(PREF_REPORT_SEQUENCE, 0L);
                SharedPreferences.Editor e = sp.edit();
                e.putLong(PREF_REPORT_SEQUENCE, seq + 1);
                e.commit();

                status.setSequence(Long.toString(seq, Character.MAX_RADIX));
                status.setReported(new RFC3339(System.currentTimeMillis()));
                status.setUrl(state.getOtaURL());

                synchronized (queueLock) {
                    queueOp(()->reportQueue.add(status));
                    if (reportQueue.size() > Constants.MAX_REPORT_QUEUE_SIZE) {
                        L.e(Constants.TAG, "Report queue overflown, removing element");
                        queueOp(()->reportQueue.remove());
                    }
                }

            }

            schedule();

        } catch (Exception e) {
            L.e(Constants.TAG, "Problem submitting report", e);
        }

    }

    public void checkAlive() {
        if (!isAlive()) {
            L.wtf(Constants.TAG, "Reporter thread has stopped!");
        }
    }

    public void schedule() {

        checkAlive();

        L.d(Constants.TAG, "Waking up reporter for new data/condition change");

        // $TODO:
        // We are not doing this within the lock because it's pointless.
        // GRL will report "blocked" after a certain failure, and until the
        // next cycle reads the GRL value. However (and the reason for this being a TODO),
        // there is one possibility of a race condition (reporter thread RT, notifying thread NT):
        // * RT - network start
        // * RT - no secondary user
        // * NT - received secondary user
        // * NT - called Reporter.schedule(), clear blocked GRL (already clear)
        // * RT - set blocked GRL
        // * RT - new cycle start
        // * RT - sleep for blocked GRL value
        grl.controlBlockedState(false);

        synchronized (schedulingLock) {
            schedulingLock.notifyAll();
        }

    }

    @TestOnly
    public void quit() {
        synchronized (schedulingLock) {
            quit = true;
            L.d(Constants.TAG, "Waking up reporter so it quits");
            schedulingLock.notifyAll();
            if (mrc != null) {
                // release the manual control, if the reporter is using it,
                // rather than the scheduling wait lock.
                mrc.advance();
            }
        }
    }

    public void run() {

        while (!quit) {

            try {
                runReports();
            } catch (Exception e) {
                L.wtf(Constants.TAG, "Reporter cycle exception", e);
                if (LibUtil.isUnderTest()) {
                    LibUtil.doThrow(e);
                }
            }

        }

    }

    private void runReports() {

        List<WInstallationRecord> toSend = null;
        Long sleep = null;

        synchronized (schedulingLock) {

            L.d(Constants.TAG, "Cycling reports");

            if (quit) {
                L.w(Constants.TAG, "Reporter: QUIT requested, leaving");
                return;
            }

            RequestDelayInfo rdi = rrs.nextTimeMsRelative();

            if (rdi.delayMs > 0 && !isRateLimitDisabled()) {
                sleep = rdi.delayMs;
                L.i(Constants.TAG, "Not ready to execute request: " + rdi.explanation);
            }

            if (sleep == null && forceDelayMs != null) {
                L.i(Constants.TAG, "Report execution postponed for " + forceDelayMs);
                sleep = forceDelayMs;
                forceDelayMs = null;
            }

            if (sleep == null && grl.isBlocked()) {
                // the last request failed because there are no secondary users
                // (or a similar condition), sleep for a predetermined amount of time.
                L.d(Constants.TAG, "Sleeping because of blocked network");
                sleep = Constants.REPORTER_DELAY_ON_BLOCKED.toMillis();
            }

            // it's simpler to just reset it here, in case
            // we end up making no requests at the end of this sleep
            // (it's unlikely, but still, can happen if reports are flushed out due to
            // the OTA URL switch).
            grl.controlBlockedState(false);

            if (sleep == null) {
                toSend = listOutstandingMessages();
            }

            if (toSend == null || toSend.isEmpty()) {

                try {

                    L.i(Constants.TAG, "Reporting sleeping for "+sleep);
                    if (!LibUtil.isUnderTest() || mrc == null) {

                        if (sleep == null) {
                            schedulingLock.wait();
                        } else {
                            schedulingLock.wait(sleep);
                        }

                    }

                } catch (Exception e) {
                    L.w("wait?", e);
                }

            }

        }

        L.d(Constants.TAG, "Reporter 2nd cycle: toSend has "+
                MyUtil.ifNotNull(toSend, List::size) + " elements");

        if (toSend != null && !toSend.isEmpty()) {
            sendReports(toSend);
        }

        if (LibUtil.isUnderTest() && mrc != null) {
            mrc.nextCycle(sleep, toSend);
        }

    }

    @NotNull
    @VisibleForTesting
    public List<WInstallationRecord> listOutstandingMessages() {

        synchronized (queueLock) {
            return queueOp(()->new ArrayList<>(reportQueue.asList()));
        }

    }

    private void sendReports(List<WInstallationRecord> toSend) {

        String url = determineUrl(toSend);

        if (url == null) {
            L.i(Constants.TAG, "No usable OTA URL, postponing");
            forceDelayMs = Duration.ofMinutes(1).toMillis();
            return;
        }

        // when assembling the final JSON, make sure to use wireFormat, which creates objects
        // with properties not needed by the backend stripped off.
        String jsRequest = app.getJsonThings().gson().toJson(toSend.stream()
                .map(WInstallationRecord::wireFormat).collect(Collectors.toList()));

        long maxId = toSend.stream().map(WInstallationRecord::getSequence)
                .map(s->Long.parseLong(s, Character.MAX_RADIX))
                .max(Long::compareTo).orElse(0L);

        CompletableFuture<Void> result = new CompletableFuture<>();

        Function<RequestInfo<JSONObject>, Request<JSONObject>> requestMaker = ri->
                new JsonRequest<>(ri.method, ri.url, (String)ri.body, ri.listener, ri.errorListener) {
                    @Override
                    protected Response<JSONObject> parseNetworkResponse(NetworkResponse response) {
                        Response<JSONObject> result;

                        if (response.data.length == 0) {
                            result = Response.success(null, HttpHeaderParser.parseCacheHeaders(response));
                        } else {
                            L.i(Constants.TAG, "Reports endpoint sent non-empty response " + response.statusCode);
                            // Refer: volley/toolbox/JsonObjectRequest.java
                            try {
                                String jsonString =
                                    new String(response.data, HttpHeaderParser.parseCharset(response.headers));
                                result = Response.success(new JSONObject(jsonString),
                                        HttpHeaderParser.parseCacheHeaders(response));
                            } catch (UnsupportedEncodingException | JSONException e) {
                                result = Response.error(new ParseError(e));
                            }
                        }

                        NetUtils.logOKNetworkResponse(response, result.result);
                        return result;
                    }

                    @Override
                    public Map<String, String> getHeaders() {
                        return app.makeExtraHeaders(ri);
                    }

                };

        Response.Listener<JSONObject> onSuccess = r->{
            if (r != null) {
                app.handleCommonResponse(r);
            }

            synchronized (queueLock) {
                // OK, we can't just remove the same amount of elements
                // that we sent out, because as we were sending them out,
                // some of the bottom elements could have been removed,
                // because the queue overflowed. So, we are stuck to
                // having to peek/remove, while holding the lock...

                while (true) {
                    WInstallationRecord next = queueOp(() -> reportQueue.peek());
                    if (next == null) {
                        break;
                    }
                    long nextSeq = Long.parseLong(next.getSequence(), Character.MAX_RADIX);
                    if (nextSeq > maxId) {
                        break;
                    }
                    queueOp(() -> reportQueue.remove());
                }
            }

            result.complete(null);

        };

        if (isRateLimitDisabled()) {
            rrs.zeroOut();
        }

        NetUtils.scheduleRequest(app, Request.Method.POST,
                app.makeURL(url, Endpoints.REPORTS), jsRequest,
                rrs, onSuccess, e->{
                    L.e(Constants.TAG, "Failed to send report data");
                    result.complete(null);
                }, requestMaker, true);

        try {
            result.get();
        } catch (Exception e) {
            L.e(Constants.TAG, "Failed wait for reporter network call result?", e);
        }

    }

    @TestOnly
    public void resetRate() {
        rrs.zeroOut();
    }

    @TestOnly
    public RequestRunState getRRS() {
        return rrs;
    }

    // We need to handle cases when the OTA URL changes, and there
    // is existing reporting data.
    // After considering various approaches, the easiest solution here is to just skip over
    // the items with a different URL. These entries will either be pushed out with time,
    // or removed on a successful w/s call. The implementation goes through the array of items
    // and will remove all items with a non-null URL that is not equal the current OTA URL,
    // or the URL of the last item.
    // The following was considered:
    // * making multiple web-service calls - complicated reconciliation after the calls return
    // * talking to "older" URLs - the oAuth token may never authenticate against the other URL
    //   anyway.
    // * keep trying to send to the old URL - it may never succeed and block the entire queue
    // * push reports that failed to send to the end of the queue, this leads to:
    //   - newer reports may be pushed out on queue overrun
    //   - complicated process of re-sequencing the reports, can't use same sequence value,
    //     sequence must never change, as they are used as de-duplication mechanism
    //   - introducing another sequence needs a separate sequencer, we don't want to have
    //     wholes in the sequence numbers reported to the backend without cause.
    private String determineUrl(List<WInstallationRecord> toSend) {

        int count = toSend.size();
        WInstallationRecord last = toSend.get(count-1);
        String url = last.getUrl();
        if (url == null) {
            url = app.getCurrentOtaUrl();
            if (url == null) {
                L.e(Constants.TAG, "No usable URL found for sending reports!");
                return null;
            }
            L.w(Constants.TAG, "Can't find URL in latest report record, assuming "+url);
        }

        // weed out any entries with different URL

        String $url = url;

        toSend.removeIf(item->{

            String itemUrl = item.getUrl();
            // URL not set - legacy entry, allow
            if (itemUrl == null) { return false; }

            if (!Objects.equals(itemUrl, $url)) {
                L.w(Constants.TAG, "Dismissing report "+item+", has different OTA URL");
                return true;
            }

            return false;

        });

        return url;

    }

    private boolean isRateLimitDisabled() {
        return rateLimitDisabled && LibUtil.isUnderTest();
    }

    @TestOnly
    public void setManualControl(ManualReportControl mrc) {
        this.mrc = mrc;
    }

}
