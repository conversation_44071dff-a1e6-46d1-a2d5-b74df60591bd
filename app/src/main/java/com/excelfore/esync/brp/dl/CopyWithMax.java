package com.excelfore.esync.brp.dl;

import java.io.IOException;
import java.security.MessageDigest;

public class CopyWithMax {

    private final MessageDigest md;
    private final long maxBytes;
    private final PKCS7Verifier verifier;
    private long hashed;

    CopyWithMax(MessageDigest md, PKCS7Verifier verifier, long maxBytes) {
        this.md = md;
        this.maxBytes = maxBytes;
        this.verifier = verifier;
    }

    public void hash(byte[] bytes) throws IOException {

        hash(bytes, bytes.length);

    }

    public void hash(byte[] bytes, int len) throws IOException {

        if (hashed + len > maxBytes) {
            len = (int) (maxBytes - hashed);
        }
        md.update(bytes, 0, len);
        verifier.feeder.write(bytes, 0, len);
        hashed += len;
    }

}
