package com.excelfore.esync.brp.client;

import static com.excelfore.esync.brp.Constants.TAG;

import com.android.volley.Request;
import com.android.volley.Response;
import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.db.UpdateProgress;
import com.excelfore.esync.brp.db.UpdateState;
import com.excelfore.esync.brp.dl.DownloadResult;
import com.excelfore.esync.brp.dl.Downloader;
import com.excelfore.esync.brp.installer.InstallResult;
import com.excelfore.esync.brp.installer.Installer;
import com.excelfore.esync.brp.model.sync_js.ClientMessage;
import com.excelfore.esync.brp.model.sync_js.ComponentState;
import com.excelfore.esync.brp.model.sync_js.OMAUpdateState;
import com.excelfore.esync.brp.model.sync_js.UserState;
import com.excelfore.esync.brp.model.sync_js.WComponentVersion;
import com.excelfore.esync.brp.model.sync_js.WInstallation;
import com.excelfore.esync.brp.model.sync_js.WInstallationRecord;
import com.excelfore.esync.brp.model.sync_js.WUpdatableEndpointInfo;
import com.excelfore.esync.brp.model.sync_js.WUpdateResponse;
import com.excelfore.esync.brp.model.sync_js.WVersionCheckRequest;
import com.excelfore.esync.brp.net.Endpoints;
import com.excelfore.esync.brp.net.NetUtils;
import com.excelfore.esync.brp.session.CurrentSession;
import com.excelfore.esync.brp.session.OperationResult;
import com.excelfore.esync.brp.session.SessionRequest;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.MyUtil;
import com.excelfore.esync.brp.util.RunnableT;
import com.google.common.util.concurrent.SettableFuture;

import org.json.JSONObject;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Objects;

public class ESyncClient extends SessionWork {

    public ESyncClient(BRPUpdateApp app, CurrentSession session) {
        super(app, session);
    }

    private void exitWithoutUpdate(SettableFuture<ClientResult> r, boolean success) {

        if (success) {
            r.set(ClientResult.success());
        } else {
            r.set(ClientResult.failure());
        }
    }

    public void doWork(SettableFuture<ClientResult> result) {
        validateOtaURL(result);
    }

    private void validateOtaURL(SettableFuture<ClientResult> result) {
        doStep(result, "validateOtaURL", () -> validateOtaURL0(result));
    }

    private void validateOtaURL0(SettableFuture<ClientResult> result) {

        // Need to validate that the OTA URL didn't just change on us.

        onUpdateState(us->{

            // $TODO this is a hack for BRP
            // If the reported version is not v1, we've updated
            if (!"v1".equals(app.getCurrentlyInstalledVersion(us))) {
                app.getMainActivity().showSuccess();
            }

            String usedUrl = us.getOtaURL();
            String newUrl = currentSession.url;
            if (usedUrl == null) {
                us.setOtaURL(newUrl);
                return;
            }

            if (!Objects.equals(usedUrl, newUrl)) {

                L.w(TAG, "OTA URL has changed! Resetting all known states!");

                us.setOtaURL(newUrl);
                destroyState(us);

                app.clientRunState.zeroOutRRS();

            }

        });

        checkManualUpdate(result);

    }

    private void destroyState(UpdateState us) {
        us.setProgress(null);
        us.setTarget(null);
        us.setReport(null);
    }

    private void checkManualUpdate(SettableFuture<ClientResult> result) {
        doStep(result, "checkManualUpdate", () -> checkManualUpdate0(result));
    }

    private void checkManualUpdate0(SettableFuture<ClientResult> result) {

        String strVersionNow = app.getCurrentlyInstalledVersion(this);

        boolean destroy = fromUpdateState(us->{

            // if there is no update target, there is nothing to do.
            if (us.getTarget() == null) {
                return false;
            }

            // let's just update the report object here, to make sure it always has the latest
            // information.
            updateCurrentReportData(us);

            String expectedSourceVersion = us.getProgress().getSourceVersion();

            // there are few possibilities to consider here.
            // new client - client with 1786 fix
            // old client - client without the fix
            // 1. new client in the middle of the update to version B
            //    source version was A, now it's either A (go on) or neither A nor B (stop update)
            // 2. new client updated to newer client
            //    after reboot the new version is either B (go on), or neither A nor B (stop update)
            // 3. old client updated to new client
            //    source version is NULL in update progress object.
            //    the new client can't know/guess what it was before the update
            //    the question becomes whether the update recorded in the updateProgress object
            //    is somehow valid or not. What we will do is not handle this here, and instead
            //    handle it at preUpdateCheck stage.

            if (expectedSourceVersion == null) {
                // case 3, we can't decide here
                return false;
            }

            // otherwise, handle case 1 or 2
            // in both cases, version must be A or B.

            UpdateProgress up = us.getProgress();
            String strVersionTarget = up.getTargetVersion();
            if (!Objects.equals(strVersionNow, expectedSourceVersion) &&
                    !Objects.equals(strVersionNow, strVersionTarget)) {

                // OK, we have encountered an unknown version, report this to the
                // backend, and vacate the update.

                L.e(TAG, "Manual update detected. Client was updating from " +
                        expectedSourceVersion + " to " + strVersionTarget +
                        ", current version is " + strVersionNow);

                us.getReport().setError(ClientMessage.F.MANUAL_UPDATE.make(strVersionNow,
                        expectedSourceVersion, strVersionTarget));
                us.getReport().setComponentState(ComponentState.UPDATE_SUCCESSFUL_WITHOUT_DATA);
                us.getReport().setUpdateState(OMAUpdateState.FAILED);

                return true;

            }

            return false;

        });

        if (destroy) {
            onUpdateState(this::destroyState);
            // let's close the session for now, any pending updates will
            // be taken up on the next run.
            result.set(ClientResult.success()); // session is over.
            return;
        }

        syncSettings(result);
    }

    private void doStep(SettableFuture<ClientResult> result, String stepName, RunnableT<Exception> step) {
        // let's also ensure that we are not on the main thread, because
        // Volley responses (for example) come from main thread
        submitWork(stepName, ()->{
            if (result.isDone()) {
                L.d(TAG, "Skipping session step "+stepName+" because result is already produced");
                return;
            }
            try {
                L.d(TAG, "Running session step "+stepName);
                step.run();
            } catch (Exception e) {
                if (result.isDone()) {
                    L.e(TAG, "Step " + stepName + " failed, but result was produced", e);
                } else {
                    result.setException(e);
                }
            }
        });
    }

    private void syncSettings(SettableFuture<ClientResult> result) {
        doStep(result, "syncSettings", () -> syncSettings0(result));
    }

    private void syncSettings0(SettableFuture<ClientResult> result) {
        app.performSyncSettings(this, ()-> syncUpdateState(result), false);
    }

    private void syncUpdateState(SettableFuture<ClientResult> r) {
        doStep(r, "check for updates", () -> syncUpdateState0(r));
    }

    private void syncUpdateState0(SettableFuture<ClientResult> r) {

        try {
            MyUtil.removeDirectoryRecursively(app.getExpandDir(), true, null);
            Files.deleteIfExists(Paths.get(app.getEsyncUpdatePayload()));
        } catch (Exception e) {
            L.e(TAG, "Failed to delete expanded directory, this can cause disk leak", e);
        }

        WVersionCheckRequest request = makeVersionRequest();

        Response.Listener<JSONObject> listener = j -> {

            try {
                WUpdateResponse response =
                        app.getJsonThings().gson().fromJson(j.toString(), WUpdateResponse.class);
                WInstallation myTarget = null;
                for (WInstallation installation : response.getTargets()) {
                    WComponentVersion target = installation.getTarget();
                    if (!Constants.NODE_NAME.equals(target.getNode())) {
                        L.e(TAG, "Unsupported update node " +
                                target.getNode() + " in service response");
                    } else {
                        myTarget = installation;
                    }
                }

                if (myTarget == null) {

                    // server didn't give us a target.
                    L.i(TAG, "Server does not have an update for this device");

                    // SOW-9 we don't want to destroy the state because we need to keep
                    // showing the "success" notifications until potentially forever
                    // destroyCurrentState();
                    exitWithoutUpdate(r, true);

                } else {
                    updateCurrentState(myTarget);
                }

            } finally {
                if (!r.isDone()) {
                    preUpdateCheck(r);
                }
            }

        };

        NetUtils.scheduleJsonObjectRequest(this, Request.Method.POST,
                app.makeCurrentURL(this, Endpoints.CHECK), request, app.clientRunState.rrsCheck,
                listener,
                (e) -> {
                    NetUtils.logVolleyError(TAG, "Failed to get latest update info", e);
                    String targetVersion = fromProgress(UpdateProgress::getTargetVersion);
                    if (targetVersion != null) {
                        preUpdateCheck(r);
                    } else {
                        exitWithoutUpdate(r, false);
                    }
                }, false);

    }

    private void updateCurrentState(WInstallation myTarget) {

        // this code handles changes to the internal state if the server
        // has reported a different state.
        //
        // The following is considered:
        // * installation record ID changed
        //   - new update
        //   - can be for the same version, when a newer campaign has been
        //     redeployed with the same version. Why - we may never know, but
        //     this can also happen after a previous campaign is aborted
        //     but for the client, it's generally impossible to know. We are going to
        //     reset the update progress in this case, but should watch out for bugs
        //     that will come if the server has decided to change the installation record
        //     for some other reason. In which case, we'll have to find a way to not
        //     vacate the progress.
        // * target version changed.
        //   - can be for a new update
        //   - can be for the same update (installation record won't change)

        onUpdateState(us->{

            // target has nothing that we ever change, so we can always override.
            us.setTarget(myTarget);

            boolean irChanged = !Objects.equals(us.getReport().getId(), myTarget.getRecordId());
            String newTargetVersion = myTarget.getTarget().getVersion();
            boolean verChanged = !Objects.equals(us.getProgress().getTargetVersion(), newTargetVersion);

            // report object is reset if installation record changes.
            if (irChanged) {
                L.i(TAG, "Resetting report object, new target installation record "+
                        myTarget.getRecordId() + ", report was for "+us.getReport().getId());
                us.setReport(null);
                us.resetSequence();
                us.getReport().setId(myTarget.getRecordId())
                        .setComponentState(ComponentState.IDLE)
                        .setUserStatus(UserState.NO_ACTION);
                updateCurrentReportData(us);
            }

            us.adjustSequence(myTarget.getStateSequence());

            // progress object is reset if target version changes, or installation record changes.
            // It was originally mandated that this should be a full reset. I considered trying to
            // salvage the download state, whether with or without consent, but after all
            // decided against it, as this would be the only recourse to recover from
            // "bad" binary problems. The only way we identify the binary is its SHA/URL, which
            // won't necessarily change after things like re-signing, etc., and if we don't reset
            // the state, the client may be stuck with the same previously downloaded "bad" binary.
            if (irChanged || verChanged) {
                L.i(TAG, "Target version changed from "+us.getProgress().getTargetVersion() +
                        " to "+newTargetVersion + ", resetting progress object");
                us.setProgress(null);
                us.getProgress().setTargetVersion(newTargetVersion);

                // Reset UserStatus and ComponentStatus in report
                us.getReport().setComponentState(ComponentState.IDLE)
                        .setUserStatus(UserState.NO_ACTION)
                        .setUpdateState(OMAUpdateState.PROGRESS);
                us.getProgress().setSourceVersion(app.getCurrentlyInstalledVersion(us));
            }

        });
    }

    private void preUpdateCheck(SettableFuture<ClientResult> r) {
        doStep(r, "pre-update check", () -> preUpdateCheck0(r));
    }

    private void preUpdateCheck0(SettableFuture<ClientResult> r) {

        WInstallation target = fromUpdateState(UpdateState::getTarget);
        String targetVersion = MyUtil.ifNotNull(target, t->t.getTarget().getVersion());

        if (targetVersion == null || target == null) {
            throw new RuntimeException("Reached download stage, but no version to download or no target");
        }

        int attempted = fromProgress(UpdateProgress::getAttempted);
        boolean applied = fromProgress(UpdateProgress::isUpdateApplied);
        L.i(TAG, "Updates attempted: "+attempted+", max attempts:"+target.getMaxRetries()+", applied:"+applied);

        if (Boolean.FALSE.equals(fromProgress(UpdateProgress::getConsent))) {
            onUpdateState(us->handleUpdateFailure(us, ClientMessage.F.BRP_CONSENT_DENIED.make(), OMAUpdateState.FAILED, ComponentState.UPDATE_FAILED_WITHOUT_DATA, true, false));
            // $TODO: because we hacked the client to not set the terminal state,
            // the allFailed is not set, and the update continues all the way to the installation
            // phase, where it's smacked with missing consent and restarts. To prevent this,
            // exit here, but these two lines shouldn't be here.
            exitWithoutUpdate(r, false);
            return;
        }

        boolean allFailed = fromUpdateState(us-> us.getReport().isTerminal());

        if (allFailed) {
            onUpdateState(us->handleUpdateFailure( us, null, null,
                    null, true, null));
            exitWithoutUpdate(r, false);
            return;
        }

        if (!applied && target.getMaxRetries() > 0 && attempted >= target.getMaxRetries()) {
            onUpdateState(us->{
                handleUpdateFailure(us, ClientMessage.F.MAX_RETRIES_REACHED.make(),
                        OMAUpdateState.FAILED, ComponentState.UPDATE_FAILED_WITH_DATA,
                        true, false);
            });
            exitWithoutUpdate(r, false);
            return;
        }

        if (target.isAborted()) {
            onUpdateState(us->{
                handleUpdateFailure(us, ClientMessage.F.CAMPAIGN_ABORTED.make(),
                        OMAUpdateState.FAILED, us.getProperFailedState(), true, false);
                us.getReport().setAborted(true);
            });
            exitWithoutUpdate(r, false);
            return;
        }

        if (!applied) {
            app.getMainActivity().showUpdate(target.getTarget().getVersion());
        }

        // if all checks pass, move on
        prepareForDownload(r);

    }

    private void prepareForDownload(SettableFuture<ClientResult> r) {
        doStep(r, "prepare for download", () -> prepareForDownload0(r));
    }

    private void prepareForDownload0(SettableFuture<ClientResult> r) {

        report(report->report.setUpdateState(OMAUpdateState.PROGRESS));
        downloadBinary(r);

    }

    private WVersionCheckRequest makeVersionRequest() {

        String myVersion = app.getCurrentlyInstalledVersion(this);

        WUpdatableEndpointInfo ueInfo = new WUpdatableEndpointInfo().setNode(Constants.NODE_NAME).setVersion(myVersion);

        onUpdateState(us->{
            if (!us.shouldCheckForUpdate()) {
                ueInfo.setNoUpdate(true);
            }
            ueInfo.setStateSequence(us.getStateSequence());
            MyUtil.whenNotNull(us.getTarget(), t->ueInfo.setInstallation(t.getRecordId()));
        });

        return new WVersionCheckRequest()
                .setCurrent(Collections.singleton(ueInfo))
                .setReportedOnly(true);
    }

    private void handleOperationResult(OperationResult opResult, SettableFuture<ClientResult> workResult,
            ComponentState failWith, Runnable onSuccess) {

        boolean success = fromUpdateState(us->{

            WInstallationRecord report = us.getReport();

            ClientMessage msg = opResult.getMessage();

            MyUtil.whenNotNull(opResult.getException(), e->L.e(TAG,
                    "Operation failed with an exception", e));

            if (opResult.isOperationFailed()) {

                OMAUpdateState uState;
                ComponentState cState;

                if (opResult.isUpdateFailed()) {
                    uState = OMAUpdateState.FAILED;
                    cState = failWith;
                } else {
                    uState = null;
                    cState = null;
                }

                handleUpdateFailure(us, msg, uState, cState, opResult.isTerminal(), opResult.isDownload());

                MyUtil.whenNotNullOr(opResult.getException(), workResult::setException, () -> workResult.set(ClientResult.failure()));
                return false;
            } else {
                report.setError(msg);
                return true;
            }

        });

        if (success) {
            onSuccess.run();
        }

    }

    private void downloadBinary(SettableFuture<ClientResult> result) {

        Downloader dl = new Downloader(app, currentSession);
        submitWork("downloader", () -> {
            DownloadResult dlResult = dl.download();
            handleOperationResult(dlResult, result, ComponentState.DOWNLOAD_FAILED,
                    ()->prepareForInstall(result, dlResult.getDownloadedFile()));
        });
    }

    private void prepareForInstall(SettableFuture<ClientResult> r, File downloadedFile) {
        doStep(r, "prepare for install", () -> prepareForInstall0(r, downloadedFile));
    }

    private void prepareForInstall0(SettableFuture<ClientResult> r, File downloadedFile) {

        Boolean consent = fromProgress(UpdateProgress::getConsent);
        L.d(TAG, "Installation consent present: " + consent);

        report(report->report.setUpdateState(OMAUpdateState.PROGRESS));

        if (Boolean.TRUE.equals(consent)) {
            installUpdate(downloadedFile, r);
            return;
        }

        if (consent == null) {
            currentSession.client.requestInstallationConsent(this);
        } else {
            app.getScheduler().scheduleNow(new SessionRequest("Negative consent found before installation"));
        }
        r.set(ClientResult.success()); // session is over.

    }

    private void installUpdate(File downloadedFile, SettableFuture<ClientResult> sessionResult) {

        // $TODO : no idea how this is handled.

        doStep(sessionResult, "update", () -> {
            Installer installer = new Installer(currentSession, app, downloadedFile);
            installer.getResult().addListener(()->{
                // future is now resolved somehow.
                InstallResult installerResult;
                try {
                    installerResult = installer.getResult().get();
                } catch (Exception e) {
                    installerResult = new InstallResult();
                    installerResult.updateFailure(null, e);
                }
                handleOperationResult(installerResult, sessionResult,
                        ComponentState.UPDATE_FAILED_WITH_DATA, ()-> {
                            // make sure the future resolves at the very end, or else resolution
                            // may kick off session end, before we finished everything.
                            // $TODO: this also means there is a gap in the implementation.
                            // the future listeners are the issue here, they don't produce
                            // a something that can be held on to to determine when the listener
                            // completes.
                            // I can't easily understand if the listeners run only when
                            // future is resolved with a result, or also with an exception.
                            // If it always runs, then we can wrap the Future.addListener() into
                            // a utility method that creates a future, and resolves it upon
                            // listener completion, calling the wrapper Runnable. If not,
                            // such futures will never resolved, and something more devious
                            // needs to be concocted.
                            sessionResult.set(ClientResult.success());
                        });
            }, app.getOurExecutorService());
            installer.install();
        });
    }

    private void handleUpdateFailure(UpdateState us, ClientMessage message,
            OMAUpdateState uState, ComponentState cState, boolean terminal,
            Boolean isDownload) {

        L.d(TAG, "huf: msg="+message+", us="+uState+", cs="+
                cState+", dl="+isDownload);

        // $TODO: the update failure handling is really fragmented.
        // this method at least contains the changes that need to be done on the
        // report object any time an error is reported

        WInstallationRecord report = us.getReport();
        UpdateProgress up = us.getProgress();
        if (message != null) { report.setError(message); }
        if (uState != null) { report.setUpdateState(uState); }
        if (cState != null) { report.setComponentState(cState); }

        // $TODO: use of terminal failure disabled
        // if we set this, the backend will stop sending us updates
        // after we reset the demo client!
//        if (terminal) {
//            report.setTerminal(true);
//        }

        report.setFailureCount(up.getAttempted());

    }

}
