package com.excelfore.esync.brp.client;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.session.CurrentSession;
import com.excelfore.esync.brp.util.DescFuture;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.function.Supplier;

/**
 * Represents an update work that must be executed as part of an update session.
 */
public abstract class SessionWork extends UpdateHelper {

    protected final CurrentSession currentSession;

    public SessionWork(BRPUpdateApp app, @NonNull CurrentSession session) {
        super(app);
        this.currentSession = session;
    }

    public CurrentSession getCurrentSession() {
        return currentSession;
    }

    /**
     * Record work with the specified name, represented by a future produced by the
     * specified supplier. This method should not be used, unless
     * {@link #submitWork(String, Runnable)} or {@link #submitWork(String, Callable)}
     * can't be used for some reason, i.e., the execution is not done on the
     * executor service returned by {@link BRPUpdateApp#getOurExecutorService()}.
     * If using this method, please follow the contract as documented in
     * {@link CurrentSession#recordWork(Supplier)}, it's paramount to follow the rules
     * for producing the future.
     * @param name name of the work
     * @param maker supplier that produces the future that resolves at the end of that work
     */
    public void recordWork(String name, Supplier<Future<?>> maker) {

        currentSession.recordWork(()->new DescFuture<>(name, maker.get()));

    }

    public void submitWork(String name, Runnable r) {
        recordWork(name, ()->app.getOurExecutorService().submit(r));
    }

    public <T> void submitWork(String name, Callable<T> c) {
        recordWork(name, ()->app.getOurExecutorService().submit(c));
    }

}
