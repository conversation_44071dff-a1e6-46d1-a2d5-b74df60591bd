package com.excelfore.esync.brp.db;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.excelfore.esync.brp.util.JsonThings;

public class UpdateProgress extends ChangeableObject {

    private String targetVersion;
    private boolean downloaded;
    private Boolean consent;
    private int downloadConsentRequest; // IOtaDownloadNotificationCode
    private int installConsentRequest; // IOtaInstallNotificationCode
    private Integer informFailure; // IOtaErrorNotificationCode
    private String eTag;
    private int verificationFailures;
    private String verificationError;
    private long verifiedTimestamp;
    private Long downloadStarted;

    /**
     * {@code true} to indicate that an update has been sent to the update engine.
     * Reset back to {@code false} when the update engine is done with it.
     */
    private boolean updateSubmitted;

    /**
     * Indicates whether an update has been applied to the system, set to {@code true}
     * specifically after the update engine indicated that the update was successful
     * (which always required a reboot before it's actually applied). It's set back to
     * {@code false} if, after a reboot, the current version read from the platform
     * does not match the target (indicating application didn't take).
     */
    private boolean updateApplied;
    /**
     * This flag is set during "version update protection" block,
     * which determines if an update can continue based on the
     * version comparison to the current version. Unusable updates
     * are eligible for immediate replacement.
     */
    private boolean unusable;

    /**
     * prn - Post Reboot Notification. {@code true} if it was delivered to the HMI.
     */
    private boolean prnShown;
    private int attempted;
    private long downloadSize;
    private boolean releaseNotesReceived;
    // indicates whether there was just an attempt to apply the update
    // during the last session. This makes a difference between reporting
    // ACTIVATION_FAILED and MAX_RETRIES_REACHED
    private boolean hadAttempt;
    // indicates whether the OTA client was ever able to tell HMI something about
    // the update (is normally set when the download consent is requested).
    // migration consideration : if we boot newer client on an older record -
    // this can only happen after *successful* update, at which point it's impossible
    // to report an error, so we don't have to worry about this value being false
    // when it really means true.
    private boolean hmiSeen;
    /**
     * Keep track of the source (current) version that the device was at
     * when the update started.
     */
    private String sourceVersion;

    // Save current notification progress,
    // then after reboot, the last progress can be displayed correctly.
    private int lastKnownDownloadProgress;

    // State of download file
    public enum DownloadFileState {
        NOT_CREATED,            // file isn't created
        CREATED,                // file was created
        NOT_FOUND,              // file was created but not found
        DELETED_ON_COMPLETE,    // file was deleted after verification completed
    }

    private DownloadFileState downloadFileState;

    public Boolean getConsent() {
        return consent;
    }

    public String getETag() {
        return eTag;
    }

    public boolean isDownloaded() {
        return downloaded;
    }

    public int getVerificationFailures() {
        return verificationFailures;
    }

    public String getVerificationError() {
        return verificationError;
    }

    public long getVerifiedTimestamp() {
        return verifiedTimestamp;
    }

    public boolean isUpdateApplied() {
        return updateApplied;
    }

    public boolean isHadAttempt() {
        return hadAttempt;
    }

    public boolean isPrnShown() {
        return prnShown;
    }

    public boolean isHmiSeen() {
        return hmiSeen;
    }

    public boolean isUnusable() {
        return unusable;
    }

    @Nullable
    public String getTargetVersion() {
        return targetVersion;
    }

    public int getAttempted() {
        return attempted;
    }

    public long getDownloadSize() {
        return downloadSize;
    }

    public DownloadFileState getDownloadFileState() {
        return downloadFileState;
    }

    public boolean isReleaseNotesReceived() {
        return releaseNotesReceived;
    }

    public int getLastKnownDownloadProgress() {
        return lastKnownDownloadProgress;
    }

    public Long getDownloadStarted() {
        return downloadStarted;
    }

    public UpdateProgress setTargetVersion(String targetVersion) {
        if (logDiff("targetVersion", this.targetVersion, targetVersion)) {
            this.targetVersion = targetVersion;
        }
        return this;
    }

    public UpdateProgress setDownloaded(boolean downloaded) {
        if (logDiff("downloaded", this.downloaded, downloaded)) {
            this.downloaded = downloaded;
        }
        return this;
    }

    public UpdateProgress setConsent(boolean consent) {
        if (logDiff("consent", this.consent, consent)) {
            this.consent = consent;
        }
        return this;
    }

    public UpdateProgress setETag(String eTag) {
        if (logDiff("eTag", this.eTag, eTag)) {
            this.eTag = eTag;
        }
        return this;
    }

    public UpdateProgress setVerificationFailures(int verificationFailures) {
        if (logDiff("verificationFailures", this.verificationFailures, verificationFailures)) {
            this.verificationFailures = verificationFailures;
        }
        return this;
    }

    public UpdateProgress setVerificationError(String verificationError) {
        if (logDiff("verificationError", this.verificationError, verificationError)) {
            this.verificationError = verificationError;
        }
        return this;
    }

    public UpdateProgress setVerifiedTimestamp(long verifiedTimestamp) {
        if (logDiff("verifiedTimestamp", this.verifiedTimestamp, verifiedTimestamp)) {
            this.verifiedTimestamp = verifiedTimestamp;
        }
        return this;
    }

    public UpdateProgress setUpdateApplied(boolean updateApplied) {
        if (logDiff("updateApplied", this.updateApplied, updateApplied)) {
            this.updateApplied = updateApplied;
        }
        return this;
    }

    public UpdateProgress setPrnShown(boolean prnShown) {
        if (logDiff("prnShown", this.prnShown, prnShown)) {
            this.prnShown = prnShown;
        }
        return this;
    }

    public UpdateProgress setHmiSeen(boolean hmiSeen) {
        if (logDiff("hmiSeen", this.hmiSeen, hmiSeen)) {
            this.hmiSeen = hmiSeen;
        }
        return this;
    }

    public UpdateProgress setUnusable(boolean unusable) {
        if (logDiff("unusable", this.unusable, unusable)) {
            this.unusable = unusable;
        }
        return this;
    }

    public UpdateProgress setHadAttempt(boolean hadAttempt) {
        if (logDiff("hadAttempt", this.hadAttempt, hadAttempt)) {
            this.hadAttempt = hadAttempt;
        }
        return this;
    }

    public UpdateProgress setAttempted(int attempted) {
        if (logDiff("attempted", this.attempted, attempted)) {
            this.attempted = attempted;
        }
        return this;
    }

    public UpdateProgress setDownloadSize(long downloadSize) {
        if (logDiff("downloadSize", this.downloadSize, downloadSize)) {
            this.downloadSize = downloadSize;
        }
        return this;
    }

    public UpdateProgress setDownloadFileState(DownloadFileState downloadFileState) {
        if (logDiff("downloadFileState", this.downloadFileState, downloadFileState)) {
            this.downloadFileState = downloadFileState;
        }
        return this;
    }

    public UpdateProgress setReleaseNotesReceived(boolean releaseNotesReceived) {
        if (logDiff("releaseNotesReceived", this.releaseNotesReceived, releaseNotesReceived)) {
            this.releaseNotesReceived = releaseNotesReceived;
        }
        return this;
    }

    public String getSourceVersion() {
        return sourceVersion;
    }

    public UpdateProgress setSourceVersion(String sourceVersion) {
        if (logDiff("sourceVersion", this.sourceVersion, sourceVersion)) {
            this.sourceVersion = sourceVersion;
        }
        return this;
    }

    public UpdateProgress setLastKnownDownloadProgress(int progress) {
        if (logDiff("lastKnownDownloadProgress", this.lastKnownDownloadProgress, progress)) {
            this.lastKnownDownloadProgress = progress;
        }
        return this;
    }

    public UpdateProgress setDownloadStarted(Long downloadStarted) {
        if (logDiff("downloadStarted", this.downloadStarted, downloadStarted, (o,n)->o == null && n != null, null)) {
            this.downloadStarted = downloadStarted;
        }
        return this;
    }

    @Override
    public String objectName() {
        return "progress";
    }

    @Override
    @NonNull
    public String toString() {
        return JsonThings.gsonNE().toJson(this);
    }

    public UpdateProgress setUpdateSubmitted(boolean submitted) {
        if (logDiff("updateSubmitted", this.updateSubmitted, submitted)) {
            this.updateSubmitted = submitted;
        }
        return this;
    }

    public boolean isUpdateSubmitted() {
        return updateSubmitted;
    }


}
