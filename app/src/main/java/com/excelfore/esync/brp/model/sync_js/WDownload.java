package com.excelfore.esync.brp.model.sync_js;


import com.excelfore.esync.brp.model.EncryptedBinaryValue;

public class WDownload {

    private String sha256;
    private String url;
    private String method;
    private EncryptedBinaryValue key;
    private long size;

    public String getSha256() {
        return sha256;
    }

    public String getUrl() {
        return url;
    }

    public String getMethod() {
        return method;
    }

    public EncryptedBinaryValue getKey() {
        return key;
    }

    public long getSize() {
        return size;
    }

    public WDownload setSize(long size) {
        this.size = size;
        return this;
    }
}
