package com.excelfore.esync.brp.installer;

import com.excelfore.esync.brp.model.sync_js.ClientMessage;
import com.excelfore.esync.brp.session.OperationResult;

public class InstallResult extends OperationResult {

    public static InstallResult success() {
        return new InstallResult();
    }

    public static InstallResult error(ClientMessage c) {
        InstallResult i = new InstallResult();
        i.updateFailure(c, null);
        return i;
    }

    public static InstallResult incomplete() {
        InstallResult i = new InstallResult();
        i.transientFailure(null, null);
        return i;
    }

    @Override
    public boolean isDownload() {
        return false;
    }
}
