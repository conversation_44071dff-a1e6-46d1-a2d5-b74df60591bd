package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.model.DMConst;
import com.excelfore.esync.brp.model.StringEnum;

public enum WConfigElementPathEnum implements StringEnum {

    PUBLIC_URL(DMConst.CFG_PUBLIC_URLS),
    WS_TIMEOUT(DMConst.OMA_READ_TIMEOUT),
    DOWNLOAD_CONNECT_TIMEOUT(DMConst.DOWNLOAD_CONNECT_TIMEOUT),
    DOWNLOAD_READ_TIMEOUT(DMConst.DOWNLOAD_READ_TIMEOUT),
    DOWNLOAD_SKIP_TIMEOUT(DMConst.DOWNLOAD_SKIP_TIMEOUT),
    SESSION_INTERVAL(DMConst.NEXT_SESSION_TIME)
    ;

    public static final StringEnum.R<WConfigElementPathEnum> resolver = new StringEnum.R<>(WConfigElementPathEnum.class);

    private final String path;

    WConfigElementPathEnum(String path) {
        this.path = path;
    }

    @Override
    public String getValue() {
        return path;
    }

}
