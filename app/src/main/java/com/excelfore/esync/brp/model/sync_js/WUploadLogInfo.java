package com.excelfore.esync.brp.model.sync_js;

public class WUploadLogInfo {
    // If false, then the hash value is considered "not ready", and the device should re-try the request
    // later, as it's being currently recalculated.
    private boolean hashReady;
    
    // SHA-256 of the uploaded portion (base64 encoded)
    private String uploadedSha256;
    
    // Size of the uploaded portion
    private long uploadedSize;

    public boolean isHashReady() {
        return hashReady;
    }

    public String getUploadedSha256() {
        return uploadedSha256;
    }

    public long getUploadedSize() {
        return uploadedSize;
    }
}
