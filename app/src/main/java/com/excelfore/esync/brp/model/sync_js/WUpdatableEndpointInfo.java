package com.excelfore.esync.brp.model.sync_js;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.LibUtil;

public class WUpdatableEndpointInfo extends WUpdatableEndpoint implements Cloneable {

    private String installation;
    private boolean noUpdate;
    private String idHash;
    private long stateSequence;

    public String getInstallation() {
        return installation;
    }

    public WUpdatableEndpointInfo setInstallation(String installation) {
        this.installation = installation;
        return this;
    }

    public boolean isNoUpdate() {
        return noUpdate;
    }

    public WUpdatableEndpointInfo setNoUpdate(boolean noUpdate) {
        this.noUpdate = noUpdate;
        return this;
    }

    public String getIdHash() {
        return idHash;
    }

    public WUpdatableEndpointInfo setIdHash(String idHash) {
        this.idHash = idHash;
        return this;
    }

    public long getStateSequence() {
        return stateSequence;
    }

    public WUpdatableEndpointInfo setStateSequence(long stateSequence) {
        this.stateSequence = stateSequence;
        return this;
    }

    public WUpdatableEndpointInfo setNode(String node) {
        super.setNode(node);
        return this;
    }

    public WUpdatableEndpointInfo setVersion(String version) {
        super.setVersion(version);
        return this;
    }

    @NonNull
    @Override
    protected WUpdatableEndpointInfo clone() {
        return (WUpdatableEndpointInfo) LibUtil.reThrow(super::clone);
    }

    @NonNull
    @Override
    public String toString() {
        WUpdatableEndpointInfo copy = clone();
        return JsonThings.gsonNE().toJson(copy);
    }
}
