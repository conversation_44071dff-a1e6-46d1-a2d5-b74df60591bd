package com.excelfore.esync.brp.net;

import org.jetbrains.annotations.NotNull;

public class EmergencyRequestRunState implements RequestRunState {


    @Override
    public void ackFinished(RequestFinishState state) { }

    @Override
    public void ackTried() {}

    @Override
    @NotNull
    public RequestDelayInfo nextTimeMsRelative() {
        return new RequestDelayInfo(0, 0, "emergency request, exempt from metering");
    }

}
