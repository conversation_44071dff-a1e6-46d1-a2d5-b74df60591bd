package com.excelfore.esync.brp.model.sync_js;

public class WComponentVersion extends WUpdatableEndpoint {

    private String type;
    private WDownload download;
    private String display;

    public String getType() {
        return type;
    }

    public WComponentVersion setType(String type) {
        this.type = type;
        return this;
    }

    public WDownload getDownload() {
        return download;
    }

    public WComponentVersion setDownload(WDownload download) {
        this.download = download;
        return this;
    }

    public String getDisplay() {
        return display;
    }

    public WComponentVersion setDisplay(String display) {
        this.display = display;
        return this;
    }

    public WComponentVersion setNode(String node) {
        super.setNode(node);
        return this;
    }

    public WComponentVersion setVersion(String version) {
        super.setVersion(version);
        return this;
    }


}
