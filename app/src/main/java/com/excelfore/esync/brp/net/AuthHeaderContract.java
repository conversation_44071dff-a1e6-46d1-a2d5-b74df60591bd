package com.excelfore.esync.brp.net;

/**
 * Instances of this interface are used in oAuth authentication
 * signalling.
 */
public interface AuthHeaderContract {

    /**
     * Indicates whether authentication token acquisition must be forced.
     * @return {@code true} if token acquisition must be forced.
     */
    boolean isForceAuth();

    /**
     * Invoked to signal that the token acquisition was impossible because no token
     * sources are available. This is generally used to satisfy a specific complaint,
     * and should be used when there were no secondary users to ask the token for.
     */
    default void tokenNoSource() {}

}
