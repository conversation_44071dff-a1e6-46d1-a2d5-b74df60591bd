package com.excelfore.esync.brp.db;

import android.database.sqlite.SQLiteAbortException;
import android.database.sqlite.SQLiteDatabaseCorruptException;
import android.database.sqlite.SQLiteException;

import androidx.room.Database;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.util.L;

import org.jetbrains.annotations.TestOnly;

@TypeConverters(Converters.class)
@Database(entities = {E_Settings.class, E_Update.class}, version = 1)
public abstract class ESyncDatabase extends RoomDatabase {

    public static final String NAME = "esync.db";

    public abstract DAOSettings settingsDAO();
    public abstract DAOUpdate updateDAO();

    public Settings loadSettings() {
        DAOSettings dao = settingsDAO();
        Long id = dao.getLastId();
        if (id == null) {
            L.w(Constants.TAG, "No properties found in the database");
            return Settings.makeDefault();
        }
        return dao.getSettings(id).contents;
    }

    public void saveSettings(Settings settings) {
        E_Settings db = new E_Settings();
        db.contents = settings;
        settingsDAO().saveSettings(db);
    }

    public String loadUpdateState() {
        DAOUpdate dao = updateDAO();

        Long id = dao.getLastId();
        if (id == null) {
            L.w(Constants.TAG, "No update state found in the database");
            return null;
        }

        return dao.getUpdate(id).contents;

    }

    public void saveUpdateState(String state) {
        E_Update db = new E_Update();
        db.contents = state;
        L.d(Constants.TAG, "Saving state:"+ state);
        updateDAO().saveUpdate(db);
    }

    @TestOnly
    public void simulateException(int c) {
        switch (c) {
            case 0:
                throw new SQLiteAbortException();
            case 6:
                throw new SQLiteDatabaseCorruptException();
            default:
                throw new SQLiteException();
        }
    }

}
