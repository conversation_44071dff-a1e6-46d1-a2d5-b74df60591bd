package com.excelfore.esync.brp.session;

import static android.content.Context.ALARM_SERVICE;

import android.app.AlarmManager;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.test.TL;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;

import org.jetbrains.annotations.NotNull;

import java.time.Duration;

public class Scheduler {

    private final com.excelfore.esync.brp.client.BRPUpdateApp app;

    public Scheduler(Context context) {
        this.app = com.excelfore.esync.brp.client.BRPUpdateApp.getApp(context);
    }

    /**
     * Requests that a session is schedule to run now, with the specified reason and
     * reschedule count. If session is running now, the session will be restarted again
     * immediately.
     * @param srb session request information
     */
    public SessionScheduleRequest scheduleNow(SessionRequest srb) {
        return scheduleAt(0, false, srb);
    }

    /**
     * Schedule a session execution in the specified amount of time.
     * @param delay duration to delay the request by, {@link Duration#ZERO} to execute immediately.
     * @param suppressIfRunning If {@code true}, and a session is currently already running, then
     *                          do not restart the session once the current session finishes.
     *                          For delayed requests, the evaluation of that fact is done when
     *                          the delay expires, and the work is attempted to be scheduled
     *                          again.
     * @param srb session request information
     */
    public SessionScheduleRequest scheduleIn(@NotNull Duration delay, boolean suppressIfRunning, SessionRequest srb) {

        return scheduleAt(SystemClock.elapsedRealtime() + delay.toMillis(), suppressIfRunning, srb);

    }

    // Normally before one session ends, next session will be scheduled.
    // Immediate session (after receiving HMI response, HMI callback registering)
    // will schedule new duplicated sessions.
    // "suppressIfRunning" is used to discard duplicated sessions after it is started.
    // But it only works when two or more sessions run at the same time (overlapped).
    // Because session will be run by executor, there's no guarantee that 
    // the session with the same start time will run at the same time.
    // So, we have to prevent duplicated session when scheduling.

    public SessionScheduleRequest scheduleAt(long startTimeMillis, boolean suppressIfRunning, SessionRequest srb) {
        return schedule(new SessionScheduleRequest(app, startTimeMillis, srb, suppressIfRunning));
    }

    SessionScheduleRequest schedule(SessionScheduleRequest what) {

        if (what == null) {
            L.e(Constants.TAG, "Scheduling invoked on <null>");
            return null;
        }

        app.getOurExecutorService().execute(()->{

            ClientRunState cs = app.clientRunState;
            SessionScheduleRequest existing;
            synchronized (cs.schedulingLock) {

                existing = cs.pendingSession;

                if (existing == null) {

                    L.i(Constants.TAG, "Scheduling "+what);
                    cs.pendingSession = what;

                } else if (existing.shouldRemainOver(what)) {

                    L.i(Constants.TAG, "Dismissing incoming session request "+what+
                            " in favor of existing "+existing);
                    what.postponedOrDismissed();
                    TL.impl.sessionRequestDismissed(what, null);
                    // don't request to schedule anything
                    return;

                } else {

                    L.i(Constants.TAG, "Dismissing existing session request "+existing+
                            " in favor of incoming "+what);
                    cs.pendingSession = what;
                    TL.impl.sessionRequestDismissed(existing, null);

                }

            }

            runScheduled();

        });

        return what;

    }

    public void runScheduled() {

        ClientRunState cs = app.clientRunState;
        SessionScheduleRequest ss;
        synchronized (cs.schedulingLock) {
            ss = cs.pendingSession;
            if (ss == null) {
                L.w(Constants.TAG, "Requested to run a session without a pending session request");
                return;
            }

            long now = SystemClock.elapsedRealtime();
            if (now < ss.when) {
                L.i(Constants.TAG, "Delaying " + ss + " in " +
                        LibUtil.num(ss.when - now) + "ms");

                TL.impl.sessionRequestScheduled(ss);
                AlarmManager alarmManager = (AlarmManager)app.getSystemService(ALARM_SERVICE);
                alarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, ss.when,
                        Constants.TAG + ":",
                        // $TODO: we are using a callback, rather than a pending intent
                        // how will that work if our app is booted out of the phone?
                        // I doubt that will actually work, as it's unclear how this can
                        // recover with application restart, unlike a pending intent.
                        this::runScheduled, new Handler(Looper.getMainLooper()));

                ss.postponedOrDismissed();

                return;

            }

            // ok, we are executing the session then.
            cs.pendingSession = null;

        }

        L.i(Constants.TAG, "Submitting "+ss+" for execution");
        app.getOurExecutorService().execute(()-> new SessionController(app).startWork(ss));

    }

}
