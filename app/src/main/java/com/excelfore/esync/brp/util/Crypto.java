package com.excelfore.esync.brp.util;

import android.util.Log;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.CRLChecker;
import com.excelfore.esync.brp.db.Settings;
import com.excelfore.esync.brp.model.PublicCertData;

import org.bouncycastle.asn1.ASN1Boolean;
import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.DERIA5String;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.asn1.DERSet;
import org.bouncycastle.asn1.DERUTF8String;
import org.bouncycastle.asn1.pkcs.EncryptedPrivateKeyInfo;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.X509ExtensionUtils;
import org.bouncycastle.cert.X509v3CertificateBuilder;
import org.bouncycastle.cert.bc.BcX509ExtensionUtils;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JceOpenSSLPKCS8DecryptorProviderBuilder;
import org.bouncycastle.operator.InputDecryptorProvider;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo;
import org.jetbrains.annotations.TestOnly;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.GeneralSecurityException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.cert.CRL;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.PKIXBuilderParameters;
import java.security.cert.PKIXCertPathChecker;
import java.security.cert.X509CertSelector;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.net.ssl.CertPathTrustManagerParameters;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

public class Crypto implements Constants {

    private static KeyPair staticKeyPair;

    @TestOnly
    public static void setStaticKeyPair(KeyPair kp) {
        if (LibUtil.isUnderTest()) {
            staticKeyPair = kp;
        }
    }

    public static SSLThings makeSSLThings(BRPUpdateApp app) {

        // Thanks to https://stackoverflow.com/a/42733858/622266

        L.i(TAG, "Generating PKI objects");

        try {

            Settings s = app.getSettings();
            Certificate tlsCACertificate = loadCertificate(s.getTlsCA());
            Certificate sigCACertificate = loadCertificate(s.getSigCA());

            Certificate intermediate;
            try (var ims = app.getAssets().open(Constants.INT_PEM)) {
                intermediate = loadCertificate(LibUtil.readFully(ims));
            }

            CRL crl = MyUtil.ifNotNull(s.getCrl(), Crypto::loadCRL);

            ensureGeneratedKey(app, intermediate);

            KeyStore tlsStore = KeyStore.getInstance("BKS");
            tlsStore.load(null, null);
            tlsStore.setCertificateEntry("ca-cert", tlsCACertificate);

            KeyStore sigStore = KeyStore.getInstance("BKS");
            sigStore.load(null, null);
            sigStore.setCertificateEntry("ca-cert", sigCACertificate);

            KeyManagerFactory kmf;

            Certificate certificate;
            try (InputStream is = Files.newInputStream(new File(app.getFilesDir(), MY_CERT_FILE).toPath())) {
                certificate = loadCertificate(is);
            }

            // The strong secure random doesn't work for some bloody reason, when running
            // on Jenkins's build slave, despite seemingly plenty of available entropy
            Random sr = LibUtil.isUnderTest() ? new Random() : SecureRandom.getInstanceStrong();
            byte[] random = new byte[64];
            sr.nextBytes(random);
            byte[] charRandom = Base64.getEncoder().encode(random);

            char [] byteChars = byteToCharArray(charRandom);

            tlsStore.setCertificateEntry("client-cert", certificate);
            tlsStore.setCertificateEntry("intermediate", intermediate);
            PrivateKey privateKey = loadPrivateKey(app.readFromEncrypted(MY_ENCRYPTED_KEY_FILE));
            tlsStore.setKeyEntry("client-key", privateKey, byteChars, new Certificate[]{certificate, intermediate});

            kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(tlsStore, byteChars);

            Arrays.fill(random, (byte) 0);
            Arrays.fill(charRandom, (byte) 0);
            Arrays.fill(byteChars, (char) 0);

            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

            if (crl != null) {
                // https://stackoverflow.com/a/38523104/622266
                PKIXCertPathChecker crlCheck = new CRLChecker(crl);
                PKIXBuilderParameters pkixParams = new PKIXBuilderParameters(tlsStore, new X509CertSelector());
                pkixParams.addCertPathChecker(crlCheck);
                pkixParams.setRevocationEnabled(false); // we do our own.
                tmf.init(new CertPathTrustManagerParameters(pkixParams));
            } else {
                tmf.init(tlsStore);
            }

            TrustManager[] tms = tmf.getTrustManagers();

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(MyUtil.ifNotNull(kmf, KeyManagerFactory::getKeyManagers), tms, new SecureRandom());

            TrustManagerFactory tmfPublic = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

            // We have switched to using full-blown bouncy castle due to platforms where
            // Android has Conscrypt in their SSL implementation.
            // However, default BouncyCastle implementation doesn't know how to load default
            // Android trust store, so we have to do it on our own (ugh).
            KeyStore publicKeyStore = KeyStore.getInstance("BKS");
            publicKeyStore.load(null, null);
            loadPublicCertificates(publicKeyStore);
            tmfPublic.init(publicKeyStore);
            TrustManager[] tmsPublic = tmfPublic.getTrustManagers();

            SSLContext sslContextPublic = SSLContext.getInstance("TLS");
            sslContextPublic.init(null, tmsPublic, new SecureRandom());
            return new SSLThings(sigStore, sslContext.getSocketFactory(), (X509TrustManager)tms[0], sslContextPublic.getSocketFactory(), (X509TrustManager)tmsPublic[0], crl);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void loadPublicCertificates(KeyStore ks) {

        File [] caFiles = new File(Constants.SYSTEM_CA_PATH).listFiles(pathname->pathname.exists() && pathname.isFile());

        if (caFiles == null) {
            L.wtf(TAG, "No CA files found in "+Constants.SYSTEM_CA_PATH);
            return;
        }

        for (File f : caFiles) {
            try (InputStream is = Files.newInputStream(f.toPath())) {
                ks.setCertificateEntry(f.getName(), loadCertificate(is));
                L.i(TAG, "Loaded CA from "+f.getAbsolutePath());
            } catch (Exception e) {
                L.e(TAG, "Failed loading certificate from "+f.getAbsolutePath(), e);
            }
        }

    }

    public static char[] byteToCharArray(byte[] bytes) {
        char[] byteChars = new char[bytes.length];
        for (int i = 0; i < byteChars.length; i++) {
            byteChars[i] = (char) bytes[i];
        }
        return byteChars;
    }

    private static void ensureGeneratedKey(BRPUpdateApp app, Certificate signingCert) throws Exception {

        File root = app.getFilesDir();
        File keyFile = new File(root, Constants.MY_ENCRYPTED_KEY_FILE);
        File certFile = new File(root, Constants.MY_CERT_FILE);

        if (keyFile.exists() && certFile.exists()) {
            L.i(TAG, "Device key/certificate present");
            return;
        }

        generateClientCredentials(app, signingCert);

    }

    private static void savePEM(OutputStream pem, String begin, String end, byte [] data, String logAs) throws Exception {
        try (Writer fw = new OutputStreamWriter(pem)) {
            String n = System.lineSeparator();
            fw.write(begin + n);
            var body = Base64.getMimeEncoder(64, n.getBytes()).encodeToString(data) + n;
            fw.write(body);
            if (logAs != null) {
                var lines = body.split("\\n");
                Log.d(TAG, "Saving PEM for "+logAs);
                for (var line : lines) {
                    Log.d(TAG, line);
                }
            }
            fw.write(end + n);
        }
    }

    public static void generateCertificate(BRPUpdateApp app, Certificate signingCert,
            FunctionT<PublicCertData, X509v3CertificateBuilder, Exception> generateBuilder,
            ConsumerT<X509CertificateHolder, Exception> onCert,
            ConsumerT<KeyPair, Exception> onKey) throws Exception {

        char [] hash;

        try (InputStream is = app.getAssets().open(SHA_BIN)) {
            var b = LibUtil.readFully(is);
            try {
                hash = LibUtil.hexChars(b);
            } finally {
                Arrays.fill(b, (byte)0);
            }
        }

        try {

            byte[] keyBytes;
            try (InputStream is = app.getAssets().open(INT_KEY_PEM)) {
                keyBytes = readPemContent(is);
            }

            // https://stackoverflow.com/a/49933828/622266
            ASN1Sequence derSeq = ASN1Sequence.getInstance(keyBytes);
            PKCS8EncryptedPrivateKeyInfo encObj =
                    new PKCS8EncryptedPrivateKeyInfo(EncryptedPrivateKeyInfo.getInstance(derSeq));
            JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
            InputDecryptorProvider decryptionProv = new JceOpenSSLPKCS8DecryptorProviderBuilder().setProvider(new BouncyCastleProvider()).build(hash);
            PrivateKeyInfo signKeyInfo = encObj.decryptPrivateKeyInfo(decryptionProv);

            Arrays.fill(hash, (char) 0);
            RSAPrivateKey signKey = (RSAPrivateKey) converter.getPrivateKey(signKeyInfo);

            KeyPair kp;
            // We need to use a static key pair for testing, as generating
            // it every time is slow, and sometimes takes too long leading to test timeouts,
            // most prominently on Jenkins.
            if (LibUtil.isUnderTest()) {
                kp = staticKeyPair;
            } else {
                L.d(TAG, "Generating new keypair (may take a while)");
                KeyPairGenerator rsa = KeyPairGenerator.getInstance("RSA");
                rsa.initialize(4096);
                kp = rsa.generateKeyPair();
                L.d(TAG, "Keypair generated");
            }

            byte[] pk = kp.getPublic().getEncoded();
            SubjectPublicKeyInfo bcPk = SubjectPublicKeyInfo.getInstance(pk);

            X509CertificateHolder signer = new X509CertificateHolder(signingCert.getEncoded());

            X509ExtensionUtils util = new BcX509ExtensionUtils();

            PublicCertData pcd = new PublicCertData(bcPk, signer);

            X509v3CertificateBuilder certGen = generateBuilder.apply(pcd);

            SubjectPublicKeyInfo authoritySPK = signer.getSubjectPublicKeyInfo();
            certGen.addExtension(Extension.subjectKeyIdentifier, false, util.createSubjectKeyIdentifier(bcPk));
            certGen.addExtension(Extension.authorityKeyIdentifier, false, util.createAuthorityKeyIdentifier(authoritySPK));
            certGen.addExtension(Extension.basicConstraints, true, new DERSequence(ASN1Boolean.getInstance(false)));

            X509CertificateHolder certHolder = certGen
                    .build(new JcaContentSignerBuilder("SHA256withRSA").build(signKey));

            onCert.accept(certHolder);
            onKey.accept(kp);
        } finally {
            Arrays.fill(hash, (char)0);
        }

    }

    public static void generateClientCredentials(BRPUpdateApp app, Certificate signingCert) throws Exception {

        L.i(TAG, "Generating new device key/certificate");

        Settings s = app.getSettings();

        generateCertificate(app, signingCert,
                pcd->{

                    X500Name subject = new X500Name("OU=xl4_device, CN=" + s.getSerialNumber());

                    BigInteger sn;
                    try {
                        sn = new BigInteger(s.getSerialNumber(), 36);
                    } catch (Exception e) {
                        L.e(TAG, "Can't convert S/N " + s.getSerialNumber() + " to number", e);
                        sn = new BigInteger(s.getSerialNumber().getBytes());
                    }

                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.DAY_OF_MONTH, -1);
                    Date notBefore = cal.getTime();
                    cal.add(Calendar.YEAR, 100);
                    Date notAfter = cal.getTime();

                    X509v3CertificateBuilder certGen = new X509v3CertificateBuilder(
                            pcd.signerCertificate.getSubject(),
                            sn, // serial no of cert
                            notBefore,
                            notAfter,
                            subject,
                            pcd.signerKeyInfo
                    );

                    ASN1EncodableVector list = new ASN1EncodableVector();
                    list.add(new DERIA5String(DEVICE_AUTH_SOTA));
                    certGen.addExtension(new ASN1ObjectIdentifier(OID_DEVICE_AUTH), false,
                            new DERSet(list).getEncoded());
                    certGen.addExtension(new Extension(new ASN1ObjectIdentifier(OID_SOTA_DEVICE_ID), false,
                            new DERUTF8String(s.getSerialNumber()).getEncoded()));

                    String tenancy = app.configDataProvider.getStaticConfigData().tenancy;

                    certGen.addExtension(new Extension(new ASN1ObjectIdentifier(OID_SOTA_TENANCY), false,
                            new DERUTF8String(tenancy).getEncoded()));

                    return certGen;

                },
                cert->{
                    try (FileOutputStream fos = new FileOutputStream(new File(app.getFilesDir(), MY_CERT_FILE))) {
                        savePEM(fos, Constants.BEGIN_CERT, Constants.END_CERT, cert.getEncoded(), MY_CERT_FILE);
                    }
                },
                key->{
                    try (OutputStream os = app.writeToEncrypted(MY_ENCRYPTED_KEY_FILE)) {
                        savePEM(os, Constants.BEGIN_X509_KEY, Constants.END_X509_KEY, key.getPrivate().getEncoded(), null);
                    }
                }
        );

    }

    public static Certificate loadCertificate(byte [] b) throws IOException, GeneralSecurityException {
        try (InputStream is = new ByteArrayInputStream(b)) {
            return loadCertificate(is);
        }
    }

    public static CRL loadCRL(byte [] b) throws IOException, GeneralSecurityException {
        try (InputStream is = new ByteArrayInputStream(b)) {
            return loadCRL(is);
        }
    }

    private static <T> T loadX509Object(InputStream pemOrDerInput,
            BiFunctionT<CertificateFactory, InputStream, T, Exception> generator)
            throws IOException, GeneralSecurityException {

        CertificateFactory certificateFactory = CertificateFactory.getInstance("X509");
        byte [] anyContent = LibUtil.readFully(pemOrDerInput);
        byte [] content;
        try (InputStream is = new ByteArrayInputStream(anyContent)) {
            content = readPemContent(is);
        } catch (Exception ignored) {
            content = anyContent;
        }
        try (ByteArrayInputStream bis = new ByteArrayInputStream(content)) {
            return LibUtil.reThrow(()->generator.apply(certificateFactory, bis));
        }

    }

    private static Certificate loadCertificate(InputStream pemOrDerInput) throws IOException, GeneralSecurityException {
        return loadX509Object(pemOrDerInput, CertificateFactory::generateCertificate);
    }

    private static CRL loadCRL(InputStream pemOrDerInput) throws IOException, GeneralSecurityException {
        return loadX509Object(pemOrDerInput, CertificateFactory::generateCRL);
    }

    private static byte[] readPemContent(InputStream pemInput) throws IOException {
        // $TODO: we don't check the type of PEM object, which we really should
        // It will fail when trying to read the corresponding DER structure, but still, we
        // could catch those problems here.
        InputStreamReader isr = new InputStreamReader(pemInput, StandardCharsets.UTF_8);
        String pem = MyUtil.readFully(isr);
        Pattern parse = Pattern.compile("(?m)(?s)^---*BEGIN.*---*$(.*)^---*END.*---*$.*");
        Matcher m = parse.matcher(pem);
        if (!m.matches()) { throw new IllegalArgumentException("no PEM armouring found"); }
        String encoded = parse.matcher(pem).replaceFirst("$1");
        return Base64.getMimeDecoder().decode(encoded);
    }

    private static PrivateKey loadPrivateKey(InputStream is) throws IOException {

        try (PEMParser pp = new PEMParser(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            JcaPEMKeyConverter jcaPEMKeyConverter = new JcaPEMKeyConverter();
            Object pemContent = pp.readObject();
            if (pemContent instanceof PEMKeyPair) {
                PEMKeyPair pemKeyPair = (PEMKeyPair) pemContent;
                KeyPair keyPair = jcaPEMKeyConverter.getKeyPair(pemKeyPair);
                return keyPair.getPrivate();
            } else if (pemContent instanceof PrivateKeyInfo) {
                PrivateKeyInfo privateKeyInfo = (PrivateKeyInfo) pemContent;
                return jcaPEMKeyConverter.getPrivateKey(privateKeyInfo);
            } else {
                throw new IllegalArgumentException("Unsupported private key format '" +
                        pemContent.getClass().getSimpleName() + '"');
            }
        }

    }

    public static X509Certificate convertFromHolder(X509CertificateHolder h) {
        try {
            return new JcaX509CertificateConverter().getCertificate(h);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void loadProviders() {
        /*
        for (Provider p : new Provider[]{new BouncyCastleProvider(), new BouncyCastleJsseProvider()}) {
            Security.removeProvider(p.getName());
            Security.insertProviderAt(p, 1);
        }
         */
    }

    public static void validateIsCA(Certificate c) throws GeneralSecurityException {

        if (!(c instanceof X509Certificate)) {
            throw new IllegalArgumentException("Certificate is not X509: " + c.getClass().getName());
        }
        X509Certificate x509 = (X509Certificate) c;
        x509.checkValidity(); // dates must be valid

        /*
        // I'm not sure this is the best idea, in case JND decides ever to add a non-self-signed
        // CA. I don't know enough on whether it's OK to do so or not, but we just check the
        // CA flag later, and consider this enough. Let the verification process freak out if
        // this is a real problem.
        if (!Objects.equals(x509.getIssuerX500Principal(), x509.getSubjectX500Principal())) {
            throw new CertificateException("CA is not self-signed");
        }
         */

        if (x509.getBasicConstraints() < 0) {
            throw new CertificateException("Basic constraints are not defined");
        }

    }

    public static class SSLThings {

        public final SSLSocketFactory sslSocketFactory;
        public final X509TrustManager sslTrustManager;
        public final SSLSocketFactory sslSocketFactoryPublic;
        public final X509TrustManager trustManagerPublic;
        public final KeyStore signatureTrustStore;
        public final CRL crl; // needed separately by PKCS7 verifier

        SSLThings(KeyStore signatureTrustStore, SSLSocketFactory sslSocketFactory, X509TrustManager sslTrustManager,
                  SSLSocketFactory sslSocketFactoryPublic, X509TrustManager trustManagerPublic, CRL crl) {
            this.signatureTrustStore = signatureTrustStore;
            this.sslSocketFactory = sslSocketFactory;
            this.sslTrustManager = sslTrustManager;
            this.sslSocketFactoryPublic = sslSocketFactoryPublic;
            this.trustManagerPublic = trustManagerPublic;
            this.crl = crl;
        }
    }


}
