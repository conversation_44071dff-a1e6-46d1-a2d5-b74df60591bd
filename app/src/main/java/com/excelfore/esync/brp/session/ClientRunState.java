package com.excelfore.esync.brp.session;

import static com.excelfore.esync.brp.Constants.TAG;

import android.os.SystemClock;

import androidx.annotation.VisibleForTesting;

import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.net.Endpoints;
import com.excelfore.esync.brp.net.GlobalRequestLimiter;
import com.excelfore.esync.brp.net.LimitedRequestRunState;
import com.excelfore.esync.brp.util.L;

import java.util.Locale;
import java.util.OptionalLong;

public class ClientRunState {

    final BRPUpdateApp app;
    final SessionController.ProtectedState eps = new SessionController.ProtectedState();

    @VisibleForTesting(otherwise = VisibleForTesting.PACKAGE_PRIVATE)
    public final Object schedulingLock = new Object(); // protects pendingSession
    @VisibleForTesting(otherwise = VisibleForTesting.PACKAGE_PRIVATE)
    public SessionScheduleRequest pendingSession;

    public final LimitedRequestRunState rrsCheckIn;
    public final LimitedRequestRunState rrsCheck;

    private final GlobalRequestLimiter allRRS = new GlobalRequestLimiter();

    public ClientRunState(BRPUpdateApp app) {
        this.app = app;
        rrsCheck = new LimitedRequestRunState(app, allRRS, Endpoints.CHECK);
        rrsCheckIn = new LimitedRequestRunState(app, allRRS, Endpoints.CHECK_IN);
    }

    // Change to get session start time rather than delay time.
    public long getNextSessionTimeMillis() {

        // We are going to look at all network requests, and see when they can be performed.
        // The minimum - wins, however, we leave a 1 second buffer to avoid thrashing.

        StringBuilder sb = new StringBuilder();
        OptionalLong countMin;

        // $TODO: (see another $TODO in Reporter)
        // if the network access is blocked, we delay the session by an entire
        // session interval. The expectation is - a secondary user will log in,
        // and the token acquisition will be unblocked. However, it's possible that
        // the user event is submitted too early, and the secondary user does not
        // yet attach, but a session started by this event runs into the blocked
        // network, and the next session is rescheduled after the full interval again.
        // That would be bad, if nothing else schedules an extra session. The real expectation
        // is that secondary user's HMI connection will schedule a session. That happens
        // well after the USR/USRHost connection is bound, so token acquisition is not
        // expected to fail with blocked network.

        if (allRRS.isBlocked()) {
            // No reason to consider network timeouts
            countMin = OptionalLong.empty();
            sb.append("Network access blocked");
        } else {
            countMin = allRRS.values().stream()
                    .peek(rrs->rrs.analyze(sb))
                    .filter(LimitedRequestRunState::hasBeenTried)
                    .mapToLong(rrs->rrs.nextTimeMsRelative().startMs)
                    .min();
        }

        // Prevent adjusting the calculated value as much as we can.
        // Later checking duplicated session in scheduler will be more accurate.
        long win = countMin.orElseGet(()->SystemClock.elapsedRealtime() +
                app.sessionInterval().toMillis());
        L.d(TAG, String.format(Locale.ENGLISH,
                "Session time calculation, result: %,d, trail: %s", win, sb));
        return win;

    }

    public void resetRRS() {
        allRRS.values().forEach(LimitedRequestRunState::resetTry);
    }

    /**
     * Zeroes out all registered limited request run states.
     * This should be used very scarcely - at this point either from the test code,
     * or when the OTA URL changes, so the client is about to talk to a different server.
     */
    public void zeroOutRRS() {
        allRRS.values().forEach(LimitedRequestRunState::zeroOut);
    }

    public CurrentSession getCurrentSession() {
        return eps.getCurrent();
    }

}
