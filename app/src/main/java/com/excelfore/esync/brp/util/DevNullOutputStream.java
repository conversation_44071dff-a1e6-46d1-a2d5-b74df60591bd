package com.excelfore.esync.brp.util;

import java.io.DataOutput;
import java.io.IOException;
import java.io.OutputStream;

public class DevNullOutputStream extends OutputStream implements DataOutput {

    @Override
    public void writeBoolean(boolean v) throws <PERSON>OException {
    }

    @Override
    public void writeByte(int v) throws IOException {
    }

    @Override
    public void writeShort(int v) throws IOException {
    }

    @Override
    public void writeChar(int v) throws <PERSON>OException {
    }

    @Override
    public void writeInt(int v) throws IOException {
    }

    @Override
    public void writeLong(long v) throws IOException {

    }

    @Override
    public void writeFloat(float v) throws IOException {

    }

    @Override
    public void writeDouble(double v) throws IOException {

    }

    @Override
    public void writeBytes(String s) throws IOException {

    }

    @Override
    public void writeChars(String s) throws IOException {

    }

    @Override
    public void writeUTF(String s) throws IOException {

    }

    @Override
    public void write(byte[] b) throws IOException {
    }

    @Override
    public void write(byte[] b, int off, int len) throws IOException {
    }

    @Override
    public void write(int b) throws <PERSON>OException {

    }
}
