package com.excelfore.esync.brp.util;

import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.model.EncryptedBinaryValue;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class JsonThings {

    private final Gson gson;

    private static final Gson gsonNE;

    static {
        GsonBuilder gb = new GsonBuilder();
        gb.registerTypeAdapter(EncryptedBinaryValue.class, new EncryptedBinaryValue.SD(null));
        gb.disableHtmlEscaping();
        if (LibUtil.isUnderTest()) {
            gb.setPrettyPrinting();
        }
        gsonNE = gb.create();
    }

    public JsonThings(BRPUpdateApp app) {
        GsonBuilder gb = new GsonBuilder();
        gb.disableHtmlEscaping();
        gb.registerTypeAdapter(EncryptedBinaryValue.class, new EncryptedBinaryValue.SD(app));
        if (LibUtil.isUnderTest()) {
            gb.setPrettyPrinting();
        }
        gson = gb.create();
    }

    public Gson gson() {
        return gson;
    }

    /**
     * This Gson will not do encryption/decryption.
     * If used with objects that contain sensitive data, the contents of that data will
     * not be available.
     * @return gson serializer/deserializer that can not do encryption.
     */
    public static Gson gsonNE() {
        return gsonNE;
    }

}
