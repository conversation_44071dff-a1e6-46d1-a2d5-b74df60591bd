package com.excelfore.esync.brp.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Transaction;

@Dao
public abstract class DAOUpdate {

    @Query("select * from E_Update where id = :id")
    abstract E_Update getUpdate(Long id);

    @Query("select max(id) from E_Update")
    abstract Long getLastId();

    @Query("delete from E_Update")
    abstract void deleteAll();

    // Keep up to 5 records
    @Query("delete from E_Update where id not in (select id from E_Update order by id desc limit 5)")
    abstract void trimRecords();

    @Insert
    abstract void insert(E_Update s);

    @Transaction
    void saveUpdate(E_Update u) {
        insert(u);
        trimRecords();
    }


}
