package com.excelfore.esync.brp.dl;

import android.os.SystemClock;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.R;
import com.excelfore.esync.brp.model.sync_js.WDownload;
import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.RunnableT;

import java.io.Closeable;
import java.io.DataOutput;
import java.io.RandomAccessFile;

import javax.annotation.Nullable;

public abstract class Decrypts<WRITER extends DataOutput & Closeable> {

    final Downloader downloader;
    long decryptedLen;
    final ConsumerT<RunnableT<Exception>, Exception> writeOp;
    final CopyWithMax hash;
    final long maxSize;

    public Decrypts(WDownload dlInfo, Downloader dl, ConsumerT<RunnableT<Exception>, Exception> writeOp, CopyWithMax hash) {
        downloader = dl;
        this.writeOp = writeOp;
        this.hash = hash;
        maxSize = dlInfo.getSize();
    }

    public void run() throws Exception {

        RateTracker rdT = new RateTracker(R.id.rSpeed);
        RateTracker wrT = new RateTracker(R.id.wSpeed);
        long allBefore = SystemClock.elapsedRealtimeNanos();

        try (RandomAccessFile reader = new RandomAccessFile(downloader.downloadFile, "r");
             WRITER writer = makeWriter()) {

            byte[] buffer = new byte[getBlockSize()];
            L.i(Constants.TAG, "Decrypting binary, buffer:" + buffer.length);

            while (true) {

                long beforeNS = SystemClock.elapsedRealtimeNanos();
                int nr = reader.read(buffer);
                rdT.recordNS(beforeNS, SystemClock.elapsedRealtimeNanos(), nr);
                if (nr < 0) { break; }
                byte [] decrypted = decrypt(buffer, nr);
                if (decrypted == null) {
                    hash.hash(buffer, nr);
                    decryptedLen += nr;
                } else {
                    if (decrypted.length > 0) {
                        beforeNS = SystemClock.elapsedRealtimeNanos();
                        writeOp.accept(()->writer.write(decrypted));
                        wrT.recordNS(beforeNS, SystemClock.elapsedRealtimeNanos(), decrypted.length);
                        hash.hash(decrypted);
                        decryptedLen += decrypted.length;
                    }
                }

                downloader.reportUIProgress(decryptedLen, maxSize, true, rdT, wrT);


            }

            byte [] coda = finishDecrypt();
            if (coda != null && coda.length > 0) {
                writeOp.accept(()->writer.write(coda));
                hash.hash(coda);
            }

        }

        trim();

        var all = new RateTracker(R.id.rwSpeed);
        all.recordNS(allBefore, SystemClock.elapsedRealtimeNanos(), decryptedLen);
        all.update(downloader.app.getMainActivity());

        L.i(Constants.TAG, "Decryption finished");

    }

    protected abstract void trim() throws Exception;

    protected abstract int getBlockSize();

    protected abstract WRITER makeWriter() throws Exception;

    @Nullable
    protected abstract byte [] decrypt(byte [] input, int len);

    @Nullable
    protected abstract byte [] finishDecrypt() throws Exception;

}
