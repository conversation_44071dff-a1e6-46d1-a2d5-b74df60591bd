package com.excelfore.esync.brp.client;

import com.excelfore.esync.brp.db.UpdateProgress;
import com.excelfore.esync.brp.db.UpdateState;
import com.excelfore.esync.brp.model.sync_js.WInstallationRecord;
import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.FunctionT;

import java.util.ArrayList;
import java.util.function.Consumer;
import java.util.function.Function;

public abstract class UpdateHelper {

    public final BRPUpdateApp app;

    public UpdateHelper(BRPUpdateApp app) {
        this.app = app;
    }

    protected void onUpdateState(ConsumerT<UpdateState, Exception> r) {
        app.getDatabase().onUpdateState(r);
    }

    protected <T> T fromUpdateState(FunctionT<UpdateState, T, Exception> r) {
        return app.getDatabase().fromUpdateState(r);
    }

    protected void report(Consumer<WInstallationRecord> c) {
        onUpdateState(us->{
            WInstallationRecord report = us.getReport();
            c.accept(report);
        });
    }

    protected <T> T fromReport(Function<WInstallationRecord, T> c) {
        return fromUpdateState(us->{
            WInstallationRecord report = us.getReport();
            return c.apply(report);
        });
    }

    protected void onProgress(Consumer<UpdateProgress> c) {
        onUpdateState(us-> c.accept(us.getProgress()));
    }

    protected <T> T fromProgress(Function<UpdateProgress, T> c) {
        return fromUpdateState(us-> c.apply(us.getProgress()));
    }

    // progress transaction
    public class PTX extends ArrayList<Consumer<UpdateProgress>> {
        public void execute() {
            if (isEmpty()) { return; }
            onProgress(up-> forEach(c->c.accept(up)));
        }
    }

    protected void updateCurrentReportData(UpdateState us) {

        us.getReport().setReportedVersion(app.getCurrentlyInstalledVersion(us));

    }

}
