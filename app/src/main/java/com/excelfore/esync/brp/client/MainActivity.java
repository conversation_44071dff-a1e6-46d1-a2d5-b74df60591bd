package com.excelfore.esync.brp.client;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.ToggleButton;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.R;
import com.excelfore.esync.brp.installer.UpdateContext;
import com.excelfore.esync.brp.session.SessionRequest;
import com.excelfore.esync.brp.util.L;

public class MainActivity extends AppCompatActivity implements Constants, View.OnClickListener {

    View consent;
    ProgressBar progress;
    Button agree;
    Button deny;
    BRPUpdateApp app;
    View available;
    TextView updateTo;
    TextView updateDone;
    ToggleButton intentType;
    Button cycle;

    private final Object uLock = new Object();
    private UpdateContext uCtx;

    public MainActivity() {
        super(R.layout.activity_main);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);

        app = BRPUpdateApp.getApp(getApplicationContext());
        ((TextView)findViewById(R.id.verValue)).setText(app.getVersion());

        available = findViewById(R.id.available);
        updateTo = findViewById(R.id.update_to);
        consent = findViewById(R.id.consent);
        progress = findViewById(R.id.progressBar);
        agree = findViewById(R.id.agree);
        deny = findViewById(R.id.disagree);
        updateDone = findViewById(R.id.successful);
        intentType = findViewById(R.id.intentType);
        cycle = findViewById(R.id.cycle);

        agree.setOnClickListener(this);
        deny.setOnClickListener(this);
        cycle.setOnClickListener(this);

        app.startWork();
        app.reportActivity(this);

    }

    public boolean useAm() {
        return intentType.isChecked();
    }

    public void showProgress() {
        runOnUiThread(()->{
            progress.setVisibility(View.VISIBLE);
            progress.setMin(0);
            progress.setProgress(0);
            progress.setMax(10000);
        });
    }

    public void hideProgress() {
        runOnUiThread(()->progress.setVisibility(View.GONE));
    }

    public void showSuccess() {
        runOnUiThread(()-> updateDone.setVisibility(View.VISIBLE));
    }

    public void showUpdate(String targetVersion) {

        runOnUiThread(()->{
            available.setVisibility(View.VISIBLE);
            updateTo.setText(targetVersion);
        });

    }

    public void hideUpdate() {

        runOnUiThread(()-> available.setVisibility(View.GONE));

    }

    public void showConsent(String ir) {
        runOnUiThread(()->{
            consent.setVisibility(View.VISIBLE);
            consent.setTag(R.id.correlatedIR, ir);
        });
    }

    public void hideConsent() {
        runOnUiThread(()->consent.setVisibility(View.GONE));
    }

    public void setProgress(long p) {
        // var p = BigInteger.valueOf(applied).multiply(BigInteger.valueOf(100)).divide(BigInteger.valueOf(total)).intValue();
        runOnUiThread(()-> {
            // Log.v(TAG, "Asking progress bar to show p of "+p);
            progress.setProgress((int)p);
        });
    }

    public void setUCtx(UpdateContext uCtx) {

        synchronized (uLock) {
            L.d(TAG, "Registering update context "+uCtx.id);
            this.uCtx = uCtx;
        }

    }

    public void removeUCtx(UpdateContext uCtx) {

        synchronized (uLock) {
            if (uCtx == this.uCtx) {
                L.d(TAG, "Unregistering update context "+uCtx.id);
                this.uCtx = null;
            } else {
                L.d(TAG, "Not unregistering update context "+uCtx.id+", current update context is "+this.uCtx.id);
            }
        }

    }


    @Override
    public void onClick(View v) {

        if (v.getId() == cycle.getId()) {

            app.getScheduler().scheduleNow(new SessionRequest("Manual cycle"));

        } else {
            Object ir = consent.getTag(R.id.correlatedIR);
            hideConsent();
            app.handleConsent(ir, v.getId() == agree.getId());
        }

    }

}
