package com.excelfore.esync.brp.db;

import android.util.Base64;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.model.sync_js.WClientConfig;
import com.excelfore.esync.brp.model.sync_js.WClientConfigElement;
import com.excelfore.esync.brp.model.sync_js.WConfigElementPathEnum;
import com.excelfore.esync.brp.util.DLog;
import com.excelfore.esync.brp.util.L;

import java.time.Duration;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

@SuppressWarnings("UnusedReturnValue")
public class Settings {

    private String serialNumber;
    private String url;
    private WClientConfig serverSays;
    private String tlsCA;
    private String sigCA;
    private String crl;

    public String getSerialNumber() {
        return serialNumber;
    }

    public Settings setSerialNumber(String serialNumber) {
        if (!Objects.equals(this.serialNumber, serialNumber)) {
            this.serialNumber = serialNumber;
        }
        return this;
    }

    public String getUrl() {
        return url;
    }

    public Settings setUrl(String url) {
        this.url = url;
        return this;
    }

    public Duration getSessionIntervalSec(Duration defaultValue) {
        return serverOrDefaultDurationSec(WConfigElementPathEnum.SESSION_INTERVAL, defaultValue);
    }

    public <T> T serverOrDefault(WConfigElementPathEnum path, T defaultValue, Function<Object, T> convert) {

        Supplier<T> makeDefault = () -> {
            DLog.d(Constants.TAG, "No value for " + path + "("+
                    path.getValue()+"), using default " + defaultValue);
            return defaultValue;
        };

        if (serverSays == null) {
            return makeDefault.get();
        }

        WClientConfigElement el = serverSays.find(path);
        if (el == null || el.getValue() == null) {
            return makeDefault.get();
        }

        try {
            T v = convert.apply(el.getValue());
            DLog.d(Constants.TAG, path.name()+"->"+v);
            return v;
        } catch (Exception e) {
            L.e(Constants.TAG, "Can't use value "+el.getValue()+" for "+path, e);
            return makeDefault.get();
        }

    }

    private long serverOrDefaultLong(WConfigElementPathEnum path, long defaultValue) {
        return serverOrDefault(path, defaultValue, v->((Number)v).longValue());
    }

    private Duration serverOrDefaultDurationMS(WConfigElementPathEnum path, Duration defaultValue) {
        return serverOrDefault(path, defaultValue, v->Duration.ofMillis(((Number)v).longValue()));
    }

    private Duration serverOrDefaultDurationSec(WConfigElementPathEnum path, Duration defaultValue) {
        return serverOrDefault(path, defaultValue, v->Duration.ofSeconds(((Number)v).longValue()));
    }

    public Duration getWSTimeout() {
        return serverOrDefaultDurationMS(WConfigElementPathEnum.WS_TIMEOUT, Constants.WS_TIMEOUT);
    }

    public Duration getDlConnectTimeout() {
        return serverOrDefaultDurationMS(WConfigElementPathEnum.DOWNLOAD_CONNECT_TIMEOUT,
                Constants.DEFAULT_DOWNLOAD_CONNECT_TIMEOUT);
    }

    public Duration getDlReadTimeout() {
        return serverOrDefaultDurationMS(WConfigElementPathEnum.DOWNLOAD_READ_TIMEOUT,
                Constants.DEFAULT_DOWNLOAD_READ_TIMEOUT);
    }

    public Duration getDlSkipTimeoutMS() {
        return serverOrDefaultDurationMS(WConfigElementPathEnum.DOWNLOAD_SKIP_TIMEOUT,
                Constants.DEFAULT_DOWNLOAD_SKIP_TIMEOUT);
    }

    public boolean isDownloadIsPublicURL() {
        return serverOrDefault(WConfigElementPathEnum.PUBLIC_URL, false, v->(Boolean)v);
    }

    public static Settings makeDefault() {

        Settings s = new Settings();
        s.serverSays = new WClientConfig();

        return s;

    }

    public byte [] getTlsCA() {
        if (tlsCA == null) { return null; }
        return Base64.decode(tlsCA, Base64.DEFAULT);
    }

    public Settings setTlsCA(byte []tlsCA) {
        this.tlsCA = Base64.encodeToString(tlsCA, Base64.DEFAULT);
        return this;
    }

    public byte [] getSigCA() {
        if (sigCA == null) { return null; }
        return Base64.decode(sigCA, Base64.DEFAULT);
    }

    public Settings setSigCA(byte [] sigCA) {
        this.sigCA = Base64.encodeToString(sigCA, Base64.DEFAULT);
        return this;
    }

    public byte [] getCrl() {
        if (crl == null) { return null; }
        return Base64.decode(crl, Base64.DEFAULT);
    }

    public Settings setCrl(byte [] crl) {
        if (crl == null) {
            this.crl = null;
        } else {
            this.crl = Base64.encodeToString(crl, Base64.DEFAULT);
        }
        return this;
    }

    public Settings setServerSays(WClientConfig serverSays) {
        this.serverSays = serverSays;
        return this;
    }
}