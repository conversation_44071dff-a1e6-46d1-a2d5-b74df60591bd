package com.excelfore.esync.brp.model.sync_js;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.db.ChangeableObject;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.google.gson.JsonElement;

import java.util.Objects;

public class WInstallation extends ChangeableObject {

    private String recordId;
    private WComponentVersion target;
    private boolean aborted;
    private int maxRetries;
    private boolean logOnFail;
    private long storageDuration;
    private String campaignId;
    private long stateSequence;

    public String getRecordId() {
        return recordId;
    }

    public WComponentVersion getTarget() {
        return target;
    }

    public boolean isAborted() {
        return aborted;
    }

    public int getMaxRetries() {

        // $TODO: BRP HACK - to prevent client shutting down after 3 attempts
        //   REMOVE!
        return 10000;
        // return maxRetries;
    }

    public WInstallation setAborted(boolean aborted) {
        if (this.aborted != aborted) {
            L.d(Constants.TAG, "Installation record "+recordId+" marked as aborted!");
            change();
            this.aborted = aborted;
        }
        return this;
    }

    public WInstallation setRecordId(String recordId) {
        this.recordId = recordId;
        return this;
    }

    public WInstallation setTarget(WComponentVersion target) {
        this.target = target;
        return this;
    }

    public boolean isLogOnFail() {
        return logOnFail;
    }

    public long getStorageDuration() {
        return storageDuration;
    }

    @Override
    public String objectName() {
        return "installation";
    }

    public long getStateSequence() {
        return stateSequence;
    }

    @Override
    @NonNull
    public String toString() {
        return JsonThings.gsonNE().toJson(this);
    }

    public String getCampaignId() {
        return campaignId;
    }

    @Override
    protected boolean reliableEquals(ChangeableObject other) {

        if (!(other instanceof WInstallation)) { return false; }

        JsonElement myTree = JsonThings.gsonNE().toJsonTree(this);
        JsonElement otherTree = JsonThings.gsonNE().toJsonTree(other);

        removeVariables(myTree);
        removeVariables(otherTree);

        return Objects.equals(myTree.toString(), otherTree.toString());

   }

   private void removeVariables(JsonElement root) {
       root.getAsJsonObject().remove("stateSequence");
       JsonElement target = root.getAsJsonObject().get("target");
       if (target == null || !target.isJsonObject()) { return; }
       JsonElement download = target.getAsJsonObject().get("download");
       if (download == null || !download.isJsonObject()) { return; }
       JsonElement key = download.getAsJsonObject().get("key");
       if (key == null || !key.isJsonObject()) { return; }
       if (key.getAsJsonObject().has("hash")) {
           key.getAsJsonObject().remove("iv");
           key.getAsJsonObject().remove("data");
       }

   }

}
