package com.excelfore.esync.brp.net;

import android.os.SystemClock;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.client.BRPUpdateApp;

import org.jetbrains.annotations.NotNull;

import java.util.Locale;

/**
 * Instances of this class carry information on success of individual requests (typically
 * segregated by type: check, check-in, release notes, etc.
 *
 * All time values are in milliseconds, and are sourced from calling
 * {@link SystemClock#elapsedRealtime()} method.
 */
public class LimitedRequestRunState implements RequestRunState {

    private final BRPUpdateApp app;
    private final Endpoints id;

    /**
     * The last time the call finished, either successfully or not.
     */
    private long lastCallTimeMs;

    /**
     * The number of times the call has been requested for delayed retries
     * since the last success.
     */
    private int retryCount;

    /**
     * Total number of invocations in this runtime that were not retried
     */
    private long totalNotRetried;
    /**
     * Total number of invocation in this runtime that were then retried
     */
    private long totalRetried;

    private boolean tried;

    private final GlobalRequestLimiter grl;

    public LimitedRequestRunState(BRPUpdateApp app, GlobalRequestLimiter grl, Endpoints id) {
        this.app = app;
        this.id = id;
        this.grl = grl;
        grl.put(id, this);
    }

    @Override
    public synchronized void ackFinished(@NonNull RequestFinishState rfs) {

        boolean updateLCT;
        boolean blocked = false;

        switch (rfs) {
            case FULL_COUNT:
                updateLCT = true;
                retryCount = 0;
                totalNotRetried++;
                break;
            case ERROR_COUNT:
                updateLCT = retryCount == 0;
                retryCount++;
                totalRetried++;
                break;
            case BLOCKED:
                updateLCT = false;
                blocked = true;
                break;
            default:
                throw new IllegalArgumentException(rfs.name());
        }

        grl.controlBlockedState(blocked);

        if (updateLCT) {
            lastCallTimeMs = Math.max(1, SystemClock.elapsedRealtime());
        }

    }

    @Override
    public synchronized void ackTried() {
        tried = true;
    }

    public void resetTry() {
        tried = false;
    }

    public boolean hasBeenTried() {
        return tried;
    }

    public StringBuilder analyze(StringBuilder sb) {

        sb.append('{').append(id)
                .append('(')
                .append(totalNotRetried)
                .append('/')
                .append(totalRetried)
                .append(')')
                .append(tried ?'+':'-');
        if (tried) {
            sb.append(nextTimeMsRelative().explanation);
        }
        sb.append('}');

        return sb;

    }

    @Override
    @NotNull
    public synchronized RequestDelayInfo nextTimeMsRelative() {

        StringBuilder sb = new StringBuilder();
        sb.append('{').append(id).append(": ");

        if (lastCallTimeMs == 0) {
            sb.append("request did not execute in this runtime}");
            return new RequestDelayInfo(0, 0, sb.toString());
        }

        long targetTime;
        long sessionInterval = app.sessionInterval().toMillis();
        sb.append(String.format(Locale.ENGLISH, "lct=%,d", lastCallTimeMs));
        if (retryCount == 0) {
            targetTime = lastCallTimeMs + sessionInterval;
            sb.append(String.format(Locale.ENGLISH, "; ec=0, + %,d", sessionInterval));
        } else {

            long retryAttempts = retryAttempts();
            long retryInterval = retryInterval();

            if (retryCount > retryAttempts) {
                sb.append("; ec=").append(retryCount).append(" > ").append(retryAttempts).append(" -> ec=0");
                sb.append(String.format(Locale.ENGLISH, "; ec=%d > %d (ec<-0); +%,d",
                        retryCount, retryAttempts, sessionInterval));
                exhaustRetries();
                targetTime = lastCallTimeMs + sessionInterval;
            } else {
                long sTime = lastCallTimeMs + sessionInterval;
                long repeatTime = lastCallTimeMs + retryInterval * retryCount;
                sb.append(String.format(Locale.ENGLISH, "; ra=%d, nst: %,d + %,d = %,d, nrt = %,d + %,d * %d = %,d",
                        retryAttempts, lastCallTimeMs, sessionInterval, sTime, lastCallTimeMs, retryInterval, retryCount, repeatTime));
                if (repeatTime > sTime) {
                    sb.append("; =>NST (ec<-0)");
                    exhaustRetries();
                    targetTime = sTime;
                } else {
                    sb.append("; =>NRT");
                    targetTime = repeatTime;
                }
            }
        }


        long rt = SystemClock.elapsedRealtime();
        long msDelay = Math.max(0, targetTime - rt);

        sb.append(String.format(Locale.ENGLISH, " = %,d; -%,d => %,d", targetTime, rt, msDelay));

        sb.append('}');

        return new RequestDelayInfo(msDelay, targetTime, sb.toString());

    }

    private long retryInterval() {
        return app.configDataProvider.getStaticConfigData().networkRetryInterval.toMillis();
    }

    private long retryAttempts() {
        return app.configDataProvider.getStaticConfigData().networkRetryLimit;
    }

    /**
     * Indicates that the retries are exhausted, and no longer should be attempted.
     */
    private synchronized void exhaustRetries() {
        retryCount = 0;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public synchronized void zeroOut() {
        lastCallTimeMs = 0;
        retryCount = 0;
        totalNotRetried = 0;
        totalRetried = 0;
        tried = false;
    }
}
