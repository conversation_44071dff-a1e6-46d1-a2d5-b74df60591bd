package com.excelfore.esync.brp.model;

import com.excelfore.esync.brp.db.UpdateState;

public class UpdateInfo {

    public final String version;
    public final String displayName;
    public final long downloadSize;
    public final String recordId;

    public UpdateInfo(UpdateState us) {

        version = us.getProgress().getTargetVersion();
        displayName = us.getTargetDisplayName();
        downloadSize = us.getTargetDownloadSize();
        recordId = us.getTarget().getRecordId();

    }

}
