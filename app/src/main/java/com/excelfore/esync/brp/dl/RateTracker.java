package com.excelfore.esync.brp.dl;

import android.widget.TextView;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.client.MainActivity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.Duration;

public class RateTracker {

    final NumberFormat nf = NumberFormat.getNumberInstance();
    long bytes;
    Duration time = Duration.ZERO;
    final int id;

    public RateTracker(int viewId) {
        nf.setGroupingUsed(true);
        nf.setMinimumIntegerDigits(9);
        id = viewId;
    }

    public void recordNS(long timeBefore, long timeAfter, long bytes) {
        time = time.plus(Duration.ofNanos(timeAfter - timeBefore));
        this.bytes += bytes;
    }

    public void recordMS(long timeBefore, long timeAfter, long bytes) {
        time = time.plus(Duration.ofMillis(timeAfter - timeBefore));
        this.bytes += bytes;
    }

    public void update(MainActivity a) {

        a.runOnUiThread(()->((TextView)a.findViewById(id)).setText(toString()));

    }

    @NonNull
    @Override
    public String toString() {

        if (this.time.compareTo(Duration.ofSeconds(1)) < 1) {
            return String.format("(Seeding %,d)", this.time.toNanos());
        }
        if (this.time.isNegative()) {
            return "NEG";
        }

        long time = this.time.toNanos();
        long lRate = new BigDecimal(bytes).multiply(BigDecimal.valueOf(1000000000)).divide(BigDecimal.valueOf(time), 0, RoundingMode.DOWN).longValue();
        return String.format("%s BPS %smin %ssec", nf.format(lRate), this.time.toMinutes(), this.time.toSecondsPart());

    }

}
