package com.excelfore.esync.brp.session;

import android.content.SharedPreferences;
import android.os.SystemClock;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.test.TL;
import com.excelfore.esync.brp.test.TestRequirements;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;

import java.time.Duration;

public class SessionScheduleRequest implements Constants {

    public final long serial;
    
    /**
     * Time when to execute the session next, in milliseconds based on
     * {@link SystemClock#elapsedRealtime()}. Expected to be 0 if session
     * shall execute immediately. Session should not run before this time
     * arrives.
     */
    public final long when;

    private boolean notificationResent;

    /**
     * Session request data provided by the caller.
     */
    public final SessionRequest srb;

    /**
     * If {@code true}, the session should not start if, at the time it should be scheduled,
     * another session is already running.
     */
    public final boolean suppressIfRunning;

    /**
     * Number of times this request has been rescheduled.
     */
    public final int rescheduleCount;

    public final BRPUpdateApp app;
    
    private final Object serialLock = new Object();

    private long getSerial(String v) {
        synchronized(serialLock) {
            SharedPreferences sp = app.getInternalState();
            long sessionId = sp.getLong(SP_U0_SESSION_ID, -1) + 1;
            L.v(TAG, "SSR origin for "+v+":"+sessionId);
            if (!sp.edit().putLong(SP_U0_SESSION_ID, sessionId).commit()) {
                L.w(TAG, "Failed to save session id!");
            }
            return sessionId;
        }
    }

    /**
     * Creates a session schedule instance.
     * @param app update app
     * @param when time to execute the session at, based on {@link SystemClock#elapsedRealtime()}
     * @param srb original session request data
     * @param suppressIfRunning {@code true} to not reschedule the session for the specified
     * reason if another session is running when this session is due to run.
     */
    public SessionScheduleRequest(BRPUpdateApp app, long when, SessionRequest srb, boolean suppressIfRunning) {
        this(app, when, srb, suppressIfRunning, 0);
    }

    private SessionScheduleRequest(BRPUpdateApp app, long when, SessionRequest srb, boolean suppressIfRunning, int rescheduleCount) {

        // prevent back-to-back sessions, they are not helpful
        // especially if there is a problem that is causing thrashing.
        // give it at least a second of cooling off time.

        if (!LibUtil.isUnderTest() || TestRequirements.normalScheduling) {
            // However, don't do this under test - under test the alarm doesn't work
            // without being driven explicitly, neither do we want to wait for a second
            // before doing work.
            long min = SystemClock.elapsedRealtime() + Duration.ofSeconds(1).toMillis();
            when = Math.max(when, min);
        }

        this.app = app;
        this.when = when;
        this.srb = srb;
        this.suppressIfRunning = suppressIfRunning;
        this.rescheduleCount = rescheduleCount;
        this.serial = getSerial(srb.reason());
    }

    public SessionScheduleRequest reschedule() {

        // NOTE that suppressIfRunning is fixed as "false" here because
        // only those that are not suppressed can be rescheduled.
        SessionScheduleRequest replacement = new SessionScheduleRequest(app, 0, srb, false, rescheduleCount + 1);
        L.d(TAG, "Rescheduling pending request "+this+" as "+replacement);
        TL.impl.sessionRequestDismissed(this, replacement);
        return replacement;

    }

    /**
     * Determines whether this session request should remain as currently
     * scheduled in presence of the specified request.
     * session request.
     * @param another competing session request.
     * @return {@code true} if this request should remain as scheduled,
     * or {@code false} if it should be replaced.
     */
    public boolean shouldRemainOver(SessionScheduleRequest another) {
        return another.when >= when;
    }

    @NonNull
    @Override
    public String toString() {
        return toString(SystemClock.elapsedRealtime());
    }

    public String toString(long now) {

        long wait = when - now;
        String sWait;
        if (wait == 0) {
            sWait = "NOW";
        } else if (wait > 0) {
            sWait = "in "+LibUtil.num(wait)+"ms";
        } else {
            sWait = "od by "+LibUtil.num(-wait)+"ms";
        }
        return getId() + "@"+LibUtil.num(when)+"("+sWait+")";

    }

    public String getId() {
        return "<"+app.getRunningId()+":"+serial+">"+"["+srb.reason()+"]";
    }

    public void postponedOrDismissed() {
        if (notificationResent) { return; }
        if (!srb.resendNotifications()) { return; }
        notificationResent = true;
    }

}
