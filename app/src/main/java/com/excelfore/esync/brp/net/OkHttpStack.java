package com.excelfore.esync.brp.net;

import android.os.SystemClock;

import com.android.volley.AuthFailureError;
import com.android.volley.Header;
import com.android.volley.Request;
import com.android.volley.toolbox.BaseHttpStack;
import com.android.volley.toolbox.HttpResponse;
import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.util.L;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.X509TrustManager;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

// we have to use our own hurl stack, because of
// https://github.com/google/volley/issues/362
// The timeouts with Android URL connection are really
// unreliable, probably because they are applied to multiple
// IP addresses; however that should not have resulted in the
// numbers actually being observed.
public class OkHttpStack extends BaseHttpStack {

    static final String HEADER_CONTENT_TYPE = "Content-Type";
    static final String HEADER_CONTENT_LENGTH = "Content-Length";

    private final SSLSocketFactory sslSocketFactory;
    private final X509TrustManager trustManager;

    /**
     * @param sslSocketFactory SSL factory to use for HTTPS connections
     */
    public OkHttpStack(SSLSocketFactory sslSocketFactory, X509TrustManager trustManager) {
        this.sslSocketFactory = sslSocketFactory;
        this.trustManager = trustManager;
    }

    @Override
    public HttpResponse executeRequest(Request<?> request, Map<String, String> additionalHeaders)
            throws IOException, AuthFailureError {
        String url = request.getUrl();
        HashMap<String, String> map = new HashMap<>();
        map.putAll(additionalHeaders);
        // Request.getHeaders() takes precedence over the given additional (cache) headers).
        map.putAll(request.getHeaders());
        URL parsedUrl = new URL(url);

        OkHttpClient client = openConnection(parsedUrl, request);

        okhttp3.Request.Builder requestBuilder = new okhttp3.Request.Builder();

        requestBuilder.url(url);

        for (Map.Entry<String, String> me : map.entrySet()) {
            if (me.getKey() == null || me.getValue() == null) { continue; }
            requestBuilder.header(me.getKey(), me.getValue());
        }
        setConnectionParametersForRequest(requestBuilder, request, map);

        okhttp3.Request okRequest = requestBuilder.build();

        long now = SystemClock.elapsedRealtime();
        Response response;
        try {
            response = client.newCall(okRequest).execute();
        } finally {
            L.d(Constants.TAG, "Request to "+url+" returned in "+
                    NetUtils.gTime(SystemClock.elapsedRealtime() - now)+"ms");
        }

        // Initialize HttpResponse with data from the HttpURLConnection.
        int responseCode = response.code();
        if (responseCode == -1) {
            // -1 is returned by getResponseCode() if the response code could not be retrieved.
            // Signal to the caller that something was wrong with the connection.
            throw new IOException("Could not retrieve response code");
        }

        ResponseBody body = response.body();

        if (body == null) {
            return new HttpResponse(responseCode, convertHeaders(response.headers()));
        }

        // Need to keep the connection open until the stream is consumed by the caller. Wrap the
        // stream such that close() will disconnect the connection.
        return new HttpResponse(
                responseCode,
                convertHeaders(response.headers()),
                (int)body.contentLength(),
                body.byteStream());
    }

    static List<Header> convertHeaders(Headers headers) {

        List<Header> headerList = new ArrayList<>(headers.size());

        for (String header : headers.names()) {
            for (String value : headers.values(header)) {
                headerList.add(new Header(header, value));
            }
        }

        return headerList;
    }

    /**
     * Opens an {@link HttpURLConnection} with parameters.
     *
     * @param url URL to open the connection to
     * @return an open connection
     */
    private OkHttpClient openConnection(URL url, Request<?> request) {

        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        int timeoutMs = request.getTimeoutMs();

        clientBuilder.connectTimeout(timeoutMs, TimeUnit.MILLISECONDS);
        clientBuilder.readTimeout(timeoutMs, TimeUnit.MILLISECONDS);

        // use caller-provided custom SslSocketFactory, if any, for HTTPS
        if ("https".equals(url.getProtocol())) {
            clientBuilder.sslSocketFactory(sslSocketFactory, trustManager);
        }

        return clientBuilder.build();
    }

    // NOTE: Any request headers added here (via setRequestProperty or addRequestProperty) should be
    // checked against the existing properties in the connection and not overridden if already set.
    @SuppressWarnings("deprecation")
    /* package */ static void setConnectionParametersForRequest(
            okhttp3.Request.Builder builder, Request<?> request, Map<String, String> setHeaders) throws AuthFailureError {
        switch (request.getMethod()) {
            case Request.Method.DEPRECATED_GET_OR_POST: {
                // This is the deprecated way that needs to be handled for backwards compatibility.
                // If the request's post body is null, then the assumption is that the request is
                // GET.  Otherwise, it is assumed that the request is a POST.
                byte[] postBody = request.getPostBody();
                if (postBody != null) {
                    postBody(builder, request, setHeaders, postBody);
                } else {
                    builder.get();
                }
            }
                break;
            case Request.Method.GET:
                // Not necessary to set the request method because connection defaults to GET but
                // being explicit here.
                builder.method("GET", null);
                break;
            case Request.Method.POST:
            {
                byte[] postBody = request.getPostBody();
                if (postBody == null) {
                    throw new IllegalArgumentException("Body must be provided for post");
                }
                postBody(builder, request, setHeaders, postBody);
            }
            break;
            default:
                throw new IllegalStateException("Unsupported method type "+request.getMethod());
        }
    }

    private static void postBody(okhttp3.Request.Builder builder, Request<?> request, Map<String, String> setHeaders, byte[] body) {
        // Set the content-type unless it was already set (by Request#getHeaders).
        if (!setHeaders.containsKey(HEADER_CONTENT_TYPE)) {
            builder.header(HEADER_CONTENT_TYPE, request.getBodyContentType());
        }
        builder.header(HEADER_CONTENT_LENGTH, String.valueOf(body.length));
        builder.post(RequestBody.create(body));
    }

}
