package com.excelfore.esync.brp.model.sync_js;

public class WReleaseNotesRequest {

    private WReleaseNotesInfo info;
    private String installation;
    private String version;

    public WReleaseNotesInfo getInfo() {
        return info;
    }

    public WReleaseNotesRequest setInfo(WReleaseNotesInfo info) {
        this.info = info;
        return this;
    }

    public String getInstallation() {
        return installation;
    }

    public WReleaseNotesRequest setInstallation(String installation) {
        this.installation = installation;
        return this;
    }

    public String getVersion() {
        return version;
    }

    public WReleaseNotesRequest setVersion(String version) {
        this.version = version;
        return this;
    }
}
