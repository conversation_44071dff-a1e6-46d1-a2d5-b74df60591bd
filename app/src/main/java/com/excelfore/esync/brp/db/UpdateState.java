package com.excelfore.esync.brp.db;

import static com.excelfore.esync.brp.util.MyUtil.with;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.model.sync_js.ComponentState;
import com.excelfore.esync.brp.model.sync_js.WInstallation;
import com.excelfore.esync.brp.model.sync_js.WInstallationRecord;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

public class UpdateState extends ChangeableObject {

    private WInstallation target;
    private UpdateProgress progress;
    private WInstallationRecord report;
    private String otaURL;

    private long stateSequence;

    private transient boolean stateSequenceChanged;

    @Nullable
    public WInstallation getTarget() {
        return target;
    }

    public WInstallation ensureTarget() {
        return ensureSubObject(target, WInstallation.class, this::setTarget);
    }

    @NonNull
    public WInstallationRecord getReport() {
        return ensureSubObject(report, WInstallationRecord.class, this::setReport);
    }

    @NonNull
    public UpdateProgress getProgress() {
        return ensureSubObject(progress, UpdateProgress.class, this::setProgress);
    }

    public UpdateState setTarget(WInstallation target) {
        changeSub(this.target, target, "target");
        this.target = target;
        return this;
    }

    private void setStateSequence(long v) {
        // special handling for state sequence being changed
        // because we don't need to increase it if it's the only thing
        // that's changed.
        if (stateSequence != v) {
            stateSequenceChanged = true;
        }
        stateSequence = v;
    }

    public UpdateState setProgress(UpdateProgress progress) {
        changeSub(this.progress, progress, "progress");
        this.progress = progress;
        return this;
    }

    public UpdateState setReport(WInstallationRecord report) {
        changeSub(this.report, report, "report");
        this.report = report;
        return this;
    }

    public String getOtaURL() {
        return otaURL;
    }

    public UpdateState setOtaURL(String otaURL) {
        if (logDiff("otaURL", this.otaURL, otaURL)) {
            this.otaURL = otaURL;
        }
        return this;
    }

    @Override
    protected List<ChangeableObject> subObjects() {
        return Arrays.asList(target, progress, report);
    }

    @Override
    public String objectName() {
        return "UpdateState";
    }

    /**
     * Specifies when it's OK to go and even check if there are new updates
     * to download. Since after the check, if there are new updates, they will
     * be pursued instead of anything that can be considered current. This stops after
     * update is delivered to the update engine.
     * @return {@code true} if new updates should be checked for, {@code false} otherwise.
     */
    public boolean shouldCheckForUpdate() {

        L.d(Constants.TAG, "Current state to consider for update acceptance:"+this);

        if (target == null) {
            L.i(Constants.TAG, "Will accept new updates, as there is no current target");
            return true;
        }

        boolean seen = getProgress().isHmiSeen();

        if (!seen) {
            L.i(Constants.TAG, "Will accept new updates, as we haven't disclosed anything to HMI yet");
            return true;
        }

        if (getProgress().isUpdateApplied()) {
            L.i(Constants.TAG, "Will accept new updates, as previous update was applied");
            return true;
        }

        // If update is unusable, we can replace.
        // this is called out explicitly, because update can become unusable after
        // it started, e.g., if there was an OTM.
        if (getProgress().isUnusable()) {
            L.i(Constants.TAG, "Will accept new updates, because current update is unusable");
            return true;
        }

        boolean terminal = with(getReport(), r->(Boolean.TRUE == r.getAborted()) || r.isTerminal());
        if (terminal) {
            L.i(Constants.TAG, "Will accept new updates, current is aborted/terminal, user dismissed failure notification");
            return true;
        }

        L.i(Constants.TAG, "Will not accept new updates");
        return false;

    }

    @Override
    @NonNull
    public String toString() {
        return JsonThings.gsonNE().toJson(this);
    }

    @Nullable
    public String getTargetDisplayName() {
        try {
            //noinspection ConstantConditions
            return getTarget().getTarget().getNode();
        } catch (Exception e) {
            L.w(Constants.TAG, "Unable to retrieve display name, state:"+this, e);
            return null;
        }
    }

    public long getTargetDownloadSize() {
        try {
            //noinspection ConstantConditions
            return getTarget().getTarget().getDownload().getSize();
        } catch (Exception e) {
            L.w(Constants.TAG, "Unable to retrieve download size from:"+this, e);
        }
        return 0;
    }

    public ComponentState getProperFailedState() {
        return getProgress().isDownloaded() ? ComponentState.UPDATE_FAILED_WITH_DATA :
                ComponentState.UPDATE_FAILED_WITHOUT_DATA;
    }

    private <T> boolean doesNotMatch(T ourValue, T theirValue, Consumer<String> logError, String msg) {

        if (!Objects.equals(ourValue, theirValue)) {
            logError.accept(msg+" : ours:"+ourValue+", request:"+theirValue);
            return true;
        }

        return false;

    }

    /**
     * Increases the current state sequence value.
     * Must only be called from {@link ESyncDatabaseWrapper}
     */
    public void increaseSequence() {
        stateSequence++;
    }

    public long getStateSequence() {
        return stateSequence;
    }

    /**
     * Resets update state sequence value ot 0.
     * Must only be called when it's determined that the
     * installation record is newly established for this device.
     */
    public void resetSequence() {
        L.w(Constants.TAG, "state sequence reset from "+stateSequence);
        setStateSequence(0);
    }

    /**
     * Adjusts the current sequence value to ensure it's
     * not less than the value provided by the server.
     * @param serverSS server known sequence state value.
     */
    public void adjustSequence(long serverSS) {
        if (stateSequence < serverSS) {
            L.w(Constants.TAG, "Adjusting state sequence "+stateSequence+" to "+serverSS);
            setStateSequence(serverSS);
        }
    }

    public boolean isStateSequenceChanged() {
        return stateSequenceChanged;
    }
}

