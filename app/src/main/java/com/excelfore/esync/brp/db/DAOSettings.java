package com.excelfore.esync.brp.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Transaction;

@Dao
public abstract class DAOSettings {

    @Query("select * from E_Settings where id = :id")
    abstract E_Settings getSettings(Long id);

    @Query("select max(id) from E_Settings")
    abstract Long getLastId();

    @Query("delete from E_Settings")
    abstract void deleteAll();

    @Insert
    abstract void insert(E_Settings s);

    @Transaction
    void saveSettings(E_Settings settings) {
        deleteAll();
        insert(settings);
    }
}
