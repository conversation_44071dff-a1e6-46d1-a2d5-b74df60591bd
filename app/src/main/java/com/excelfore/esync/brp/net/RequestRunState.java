package com.excelfore.esync.brp.net;

import org.jetbrains.annotations.NotNull;

public interface RequestRunState {

    /**
     * Inform the request state that the request has concluded and should be counted towards
     * the request rate.
     * @param state the state with which the request has finished, influences the decision
     * made to when it can/should be re-attempted again.
     */
    void ackFinished(RequestFinishState state);

    /**
     * Inform the request state that request was considered for execution. This method must be
     * called before concluding informing the state of request's conclusion using
     * {@link #ackFinished(RequestFinishState)} )}, and can be called any number of times without side
     * effects until the request is concluded.
     */
    void ackTried();

    /**
     * Determines how long should pass before the network call this state is associated with can be
     * made (again). Returns milliseconds until the next call, or {@code 0}, if the call can be made
     * right now.
     *
     * @return information on whether the request should be delayed, and by how long.
     */
    @NotNull
    RequestDelayInfo nextTimeMsRelative();

    default void zeroOut() {}

}
