package com.excelfore.esync.brp.model;

import com.excelfore.esync.brp.util.LibUtil;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;

public class StringEnumSD implements JsonSerializer<StringEnum>, JsonDeserializer<StringEnum> {

    @Override
    public StringEnum deserialize(JsonElement json, Type typeOfT,
                                      JsonDeserializationContext context) throws JsonParseException {

        return LibUtil.reThrow(()->{
            //noinspection unchecked,rawtypes
            StringEnum.R<? extends StringEnum> f = (StringEnum.R)((Class) typeOfT).getDeclaredField("resolver").get(null);
            if (f == null) { throw new IllegalArgumentException("<resolver> is not static?"); }
            return f.fromString(json.getAsString());
        });

    }

    @Override
    public JsonElement serialize(StringEnum src, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(src.getValue());
    }
}
