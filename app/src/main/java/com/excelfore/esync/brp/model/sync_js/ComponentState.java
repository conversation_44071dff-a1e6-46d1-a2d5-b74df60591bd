package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.model.NumericOMAEnum;
import com.excelfore.esync.brp.model.OMAEnumSD;
import com.google.gson.annotations.JsonAdapter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Represents component update state for an installation record.
 * See https://excelfore.atlassian.net/wiki/spaces/ESYNC/pages/468516886/Updates+Reporting
 * for documentation of the individual status values.
 */
@JsonAdapter(OMAEnumSD.class)
public enum ComponentState implements NumericOMAEnum {

    IDLE(10),
    READY_TO_DOWNLOAD(15),
    DOWNLOAD_FAILED(20),
    DOWNLOAD_PROGRESSING(30),
    DOWNLOAD_COMPLETE(40),
    PREPARING_UPDATE (45),
    READY_TO_UPDATE(50),
    UPDATE_PROGRESSING(60),
    UPDATE_FAILED_WITH_DATA(70),
    UPDATE_FAILED_WITHOUT_DATA(80),
    UPDATE_SUCCESSFUL_WITH_DATA(90),
    UPDATE_SUCCESSFUL_WITHOUT_DATA(100);

    int omaCode;

    private final static Map<Integer, ComponentState> byOmaCode =
            new ConcurrentHashMap<>();

    static {
        for (ComponentState ps : ComponentState.values()) {
            byOmaCode.put(ps.omaCode, ps);
        }
    }

    ComponentState(int omaCode) {
        this.omaCode = omaCode;
    }

    /**
     * Convenience method to determine if the status represents a failed update.
     * @return {@code true} if this update status represents a failed update,
     * {@code false} otherwise.
     */
    public boolean isFailed() {
        return this == DOWNLOAD_FAILED || this == UPDATE_FAILED_WITH_DATA ||
                this == UPDATE_FAILED_WITHOUT_DATA;
    }

    /**
     * Convenience method to determine if the status represents a successfully completed
     * update.
     * @return {@code true} if the update has completed successfully, {@code false} otherwise.
     */
    public boolean isSuccessful() {

        return this == UPDATE_SUCCESSFUL_WITH_DATA ||
                this == UPDATE_SUCCESSFUL_WITHOUT_DATA;

    }

    /**
     * Convenience method to determine if the status of the update indicates that the client
     * has a complete binary. This includes failed or successful statuses that assume that the binary
     * must be present on the client.
     * @return {@code true} if the binary is considered available on the client, {@code false} otherwise.
     */
    public boolean hasFullBinary() {

        return this == DOWNLOAD_COMPLETE ||
                this == UPDATE_FAILED_WITHOUT_DATA ||
                this == UPDATE_SUCCESSFUL_WITH_DATA ||
                this == READY_TO_UPDATE ||
                this == UPDATE_PROGRESSING;

    }

    /**
     * Returns the instance of this enumeration for the specified OMA code, or {@code null} if code is not found,
     * @param omaCode OMA code representing the installation record status.
     * @return enum instance, or {@code null} if no instance corresponds to the specified code.
     */
    public static ComponentState getByOmaCode(int omaCode) {
        return byOmaCode.get(omaCode);
    }

    /**
     * Returns OMA code of this status instance.
     * @return OMA code for this component state
     */
    @Override
    public int getOmaCode() {
        return omaCode;
    }

    public static ComponentState convertToFailed(ComponentState old) {
        if (old == null) { return UPDATE_FAILED_WITHOUT_DATA; }
        if (old.hasFullBinary()) {
            return UPDATE_FAILED_WITH_DATA;
        } else {
            return UPDATE_FAILED_WITHOUT_DATA;
        }
    }

}
