package com.excelfore.esync.brp.util;

import java.util.Formatter;
import java.util.Locale;

public class CFormat {

    private final String pattern;

    public CFormat(String pattern) {
        this.pattern = pattern;
    }

    public String format(Object...args) {

        StringBuilder sb = new StringBuilder();
        Formatter f = new Formatter(sb);
        f.format(Locale.US, pattern, args);
        return sb.toString();

    }

    @Override
    public String toString() {
        return pattern;
    }

}
