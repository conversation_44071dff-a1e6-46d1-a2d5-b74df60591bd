package com.excelfore.esync.brp.util;

import android.os.SystemClock;

import com.excelfore.esync.brp.Constants;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * This class facilitates logging messages, while omitting messages that may repeat
 * frequently, and therefore both useless, and clog the logs.
 */
public class DLog {

    private static final ConcurrentMap<String, Long> knownMessages = new ConcurrentHashMap<>();
    public static final Duration CACHE_TIME = Duration.ofSeconds(5); // cache time of 5 secs

    // facilities are only provided for verbose/debug/info messages
    // facilities are only for messages without exceptions

    public static int v(String tag, String msg) {
        if (deDup(tag, msg)) { return 0; }
        return L.v(tag, msg);
    }

    public static int d(String tag, String msg) {
        if (deDup(tag, msg)) { return 0; }
        return L.d(tag, msg);
    }

    public static int i(String tag, String msg) {
        if (deDup(tag, msg)) { return 0; }
        return L.i(tag, msg);
    }

    public static void zeroOut() {
        knownMessages.clear();
    }

    public static void clean() {
        long min = SystemClock.elapsedRealtime() - CACHE_TIME.toMillis();
        int cleaned = 0;

        for (Map.Entry<String, Long> me : knownMessages.entrySet()) {
            Long v = me.getValue();
            String k = me.getKey();
            if (v == null || v < min) {
                if (knownMessages.remove(k, v)) {
                    cleaned++;
                }
            }
        }

        if (cleaned > 0) {
            L.d(Constants.TAG, "Cleaned "+cleaned+" duplicate log messages");
        }
    }

    private static boolean deDup(String tag, String msg) {

        String key = tag + msg;
        MutableBoolean block = new MutableBoolean();
        Long timeNow = SystemClock.elapsedRealtime();
        knownMessages.compute(key, (k, v)->{
            if (v != null && timeNow - v < CACHE_TIME.toMillis()) {
                block.set(true);
            }
            return timeNow;
        });
        return block.get();

    }

}
