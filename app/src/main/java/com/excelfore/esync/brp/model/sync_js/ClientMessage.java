package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.util.CFormat;
import com.excelfore.esync.brp.util.L;

import java.util.Arrays;

public class ClientMessage {

    private final F format;
    private final Object[] arguments;

    public ClientMessage(F f, Object...arguments) {
        this.format = f;
        this.arguments = Arrays.copyOf(arguments, arguments.length);
    }

    public String getString() {
        try {
            return format.fmt.format(arguments);
        } catch (Exception e) {
            L.e(Constants.TAG, "Failed to format message, key "+format.fmt.toString(), e);
            return format.fmt.toString();
        }
    }

    public enum F {

        DOWNLOAD_CONSENT_REQUESTED("Download consent requested"),
        VERIFYING_BINARY_INTEGRITY("Verifying binary integrity: %s"),
        FILE_SIZE_MISMATCH("Previously downloaded file %s has length %,d, but expected to be %,d"),
        SHA_MISMATCH("SHA-256 mismatch for %s. Expected: %s, calculated: %s"),
        VERIFICATION_MAX_REACHED("Current verification count %d is at or over maximum of %d for %s, latest known verification error: %s"),
        DECRYPTION_FAILED("Decryption failed: %s"),
        DOWNLOAD_HTTP_CODE("Downloading %s returned HTTP code %d"),
        DOWNLOAD_STORE_ERR("Problem storing downloaded binary: %s"),
        DOWNLOAD_NETWORK_ERROR("Network error downloading %s: %s"),
        UPDATE_FAILED_UA("Update agent reported target update failed: %s"),
        MAX_RETRIES_REACHED("No more tries left to update"),
        CAMPAIGN_ABORTED("Encountered aborted installation record"),
        DOWNLOADED("Download complete: %s"),
        UPDATE_ENGINE("Sending installation to update engine"),
        REBOOT_REQUIRED("Update engine finished update, reboot required"),
        MANUAL_UPDATE("Unexpected current device version %s, while updating from %s to %s"),
        BRP_CONSENT_REQUESTED("Requesting consent from the end-user"),
        BRP_CONSENT_DENIED("Installation consent denied"),

        ;

        final CFormat fmt;

        F(String fmt) {
            this.fmt = new CFormat(fmt);
        }

        public ClientMessage make(Object...arguments) {
            return new ClientMessage(this, arguments);
        }

        public boolean matches(String msg) {

            if (msg == null || msg.length() == 0) { return false; }

            // $TODO: this won't always work correctly; this won't work
            // if the substring search finds something in the variable piece.
            // However, this should be very unlikely, and I didn't feel like
            // spending time on making this more precise, considering this is
            // for testing purposes only.

            int i = 0;
            int j = 0;

            String mine = this.fmt.toString();

            while (true) {

                char c1 = mine.charAt(i++);

                if (c1 == '%') {

                    while (c1 == '%') {

                        // advance i to end of '%x' sequence
                        while (!Character.isAlphabetic(c1)) {
                            c1 = mine.charAt(i++);
                        }

                        if (i >= mine.length()) { return true; }

                        c1 = mine.charAt(i);

                    }

                    // is there another '%' sequence?
                    int pct = mine.indexOf('%', i);
                    String search = pct < 0 ? mine.substring(i) : mine.substring(i, pct);
                    j = msg.indexOf(search, j+1);
                    if (j < 0) {
                        return false;
                    }

                    continue;

                }

                char c2 = msg.charAt(j++);
                if (c1 != c2) { return false; }

                if (i == mine.length()) {
                    return j == msg.length();
                }

            }

        }

    }

}
