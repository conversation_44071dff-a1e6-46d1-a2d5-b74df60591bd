package com.excelfore.esync.brp.model.sync_js;

public class WDeviceLogRequest {
    // Specifies time, in milliseconds, how long shall the device capture the live logs for.
    private long captureDuration;
    
    // Log filter expression, can be `null` to indicate no filtering.
    private String filter;

    // Indication whether the device is being asked to start log gathering 
    // on the specified sequence value (TRUE) or cancel log gathering (FALSE)
    private boolean request;

    // The log request is for the specified sequence.
    private long sequence;

    // Specifies time, in milliseconds, how long shall the device keep the capture log for before removing it.
    private long storageDuration;
    

    public long getCaptureDuration() {
        return captureDuration;
    }

    public String getFilter() {
        return filter;
    }

    public boolean getRequest() {
        return request;
    }

    public long getSequence() {
        return sequence;
    }

    public long getStorageDuration() {
        return storageDuration;
    }

    public String toString() {
        return com.excelfore.esync.brp.util.JsonThings.gsonNE().toJson(this);
    }

}
