
package com.excelfore.esync.brp.util;

import java.util.function.Consumer;

public class MutableBoolean implements Consumer<Boolean> {

    public boolean value;

    public MutableBoolean() {
    }

    public MutableBoolean(boolean value) {
        this.value = value;
    }

    public void setTrue() {
        value = true;
    }

    public void setFalse() {
        value = false;
    }

    public void set(boolean value) {
        this.value = value;
    }

    public boolean isTrue() {
        return value;
    }

    public boolean isFalse() {
        return !value;
    }

    public boolean get() {
        return value;
    }

    public void invert() {
        value = !value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    @Override
    public void accept(Boolean aBoolean) {
        value = aBoolean;
    }
}
