package com.excelfore.esync.brp.dl;

import com.excelfore.esync.brp.util.MyUtil;

import java.util.Objects;

public class ContentRangeHeader {

    final long from;
    final long to;
    final long total;

    public ContentRangeHeader(String range) {

        // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range

        String original = range;

        range = MyUtil.sTrim(range);
        Objects.requireNonNull(range, "Null range value");

        if (!range.startsWith("bytes ")) {
            throw new IllegalArgumentException("Unknown unit in range "+range);
        }

        if ((range = MyUtil.sTrim(range.substring(6))) == null) {
            throw new IllegalArgumentException("No range/size data in "+original);
        }

        int slash = range.indexOf('/');
        if (slash < 0) {
            throw new IllegalArgumentException("No slash found in "+original);
        }

        String ranges = MyUtil.sTrim(range.substring(0, slash));
        String size = MyUtil.sTrim(range.substring(slash+1));

        if ("*".equals(ranges)) {
            try {
                //noinspection ConstantConditions
                total = Long.parseLong(size);
            } catch (Exception e) {
                throw new IllegalArgumentException("Can not parse size from "+original, e);
            }
            from = 0;
            to = total - 1;
        } else {

            if ("*".equals(size)) {
                throw new IllegalArgumentException("Unknown sizes are not supported : "+original);
            }

            try {
                //noinspection ConstantConditions
                total = Long.parseLong(size);
            } catch (Exception e) {
                throw new IllegalArgumentException("Can not parse size from "+original, e);
            }

            if (ranges == null) {
                throw new IllegalArgumentException("No range found in "+original);
            }

            int dash = ranges.indexOf('-');
            if (dash < 0) {
                throw new IllegalArgumentException("No range dash found in "+original);
            }

            String from = ranges.substring(0, dash);
            String to = ranges.substring(dash+1);

            try {
                this.from = Long.parseLong(from);
            } catch (Exception e) {
                throw new IllegalArgumentException("Could not parse range start in "+original, e);
            }
            try {
                this.to = Long.parseLong(to);
            } catch (Exception e) {
                throw new IllegalArgumentException("Could not parse range end in "+original, e);
            }

            if (total <= 0 || this.from <= 0 || this.to <= 0 || this.from > this.to || this.to >= total) {
                throw new IllegalArgumentException("Nonsensical range value "+original);
            }

        }

    }

    public long getFrom() {
        return from;
    }

    public long getTo() {
        return to;
    }

    public long getTotal() {
        return total;
    }
}
