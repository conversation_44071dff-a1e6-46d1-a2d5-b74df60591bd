package com.excelfore.esync.brp.cfg;

import android.util.Base64;

public class IDInfo {

    public final String serialNumber;
    public final String otaURL;
    public final byte [] tlsCA;
    public final byte [] signatureCA;
    public final byte [] crl;
    public final boolean ignoreURL;


    public IDInfo(String serialNumber, String otaURL, boolean ignoreURL, byte [] tlsCA,
            byte [] signatureCA, byte [] crl) {

        this.serialNumber = serialNumber;
        this.otaURL = otaURL;
        this.ignoreURL = ignoreURL;
        this.tlsCA = tlsCA;
        this.signatureCA = signatureCA;
        this.crl = crl;

    }

    public String tlsCAStr() {
        return base64(tlsCA);
    }

    private String base64(byte [] data) {
        if (data == null) { return null; }
        return Base64.encodeToString(data, Base64.DEFAULT);
    }

    public String signatureCAStr() {
        return base64(signatureCA);
    }

    public String crlStr() {
        return base64(crl);
    }

}
