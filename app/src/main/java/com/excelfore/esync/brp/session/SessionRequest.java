package com.excelfore.esync.brp.session;

public class SessionRequest {

    /**
     * Reason for starting the session
     */
    private final String reason;

    /**
     * Whether notifications should be resent if scheduling can not be done immediately
     */
    private final boolean resendNotification;

    public SessionRequest(String reason) {
        this(reason, false);
    }

    public SessionRequest(String reason, boolean resendNotification) {
        this.reason = reason;
        this.resendNotification = resendNotification;
    }

    @Override
    public String toString() {

        return "SessionRequest{" +
                "reason='" + reason + '\'' +
                ", resentNotification=" + resendNotification +
                '}';

    }

    public String reason() {
        return reason;
    }

    public boolean resendNotifications() {
        return resendNotification;
    }

}
