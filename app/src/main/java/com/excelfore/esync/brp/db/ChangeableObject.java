package com.excelfore.esync.brp.db;


import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.function.Function;

public abstract class ChangeableObject {

    private transient boolean changed;
    private transient boolean open;

    final void open() {
        open = true;
        subObjects().forEach(t->{
            if (t != null) { t.open(); }
        });
    }

    final void close() {
        open = false;
        subObjects().forEach(t->{
            if (t != null) { t.close(); }
        });
    }

    final boolean isOpen() {
        return open;
    }

    protected void requireOpened() {
        if (!open) {
            throw new IllegalStateException("The state is not checked out, and can not be changed. Use after release?");
        }
    }

    protected void change() {
        requireOpened();
        changed = true;
    }

    protected void changeSub(ChangeableObject oldSub, ChangeableObject newSub, String subName) {
        requireOpened();
        if (oldSub == null && newSub == null) {
            L.d(Constants.TAG, objectName() + "."+subName+", no change (null->null)");
            return;
        }
        if (newSub != null) {
            newSub.open();
        }
        if (oldSub != null && newSub != null && oldSub.reliableEquals(newSub)) {
            L.d(Constants.TAG, objectName() + "."+subName+", no change");
            newSub.changed = false;
            return;
        }
        change();
        L.d(Constants.TAG, objectName() + " changed - "+subName+" replaced\nOld contents: "+oldSub+"\nNew contents:"+newSub);
    }

    protected boolean reliableEquals(ChangeableObject other) {
        if (other == null) { return false; }
        return toString().equals(other.toString());
    }

    public final boolean isChanged() {
        return changed || subObjects().stream().filter(Objects::nonNull).anyMatch(ChangeableObject::isChanged);
    }

    public abstract String objectName();

    protected  <T> boolean logDiff(String name, T curValue, T newValue) {
        return logDiff(name, curValue, newValue, null);
    }

    protected <T> boolean logDiff(String name, T curValue, T newValue, Function<T, T> display) {
        return logDiff(name, curValue, newValue, null, display);
    }

    // changeCheck - returns TRUE if values are DIFFERENT
    protected <T> boolean logDiff(String name, T curValue, T newValue,
            BiPredicate<T, T> changeCheck, Function<T, T> display) {
        requireOpened();

        if (changeCheck == null) {
            changeCheck = (curV, newV) -> !Objects.equals(curV, newV);
        }

        boolean diff = changeCheck.test(curValue, newValue);
        if (display == null) { display = s->s; }
        T dCurValue = display.apply(curValue);
        T dNewValue = display.apply(newValue);
        if (diff) {
            L.d(Constants.TAG, objectName() + ": "+name+": changed "+dCurValue + " -> " + dNewValue);
            changed = true;
        } else {
            L.d(Constants.TAG, objectName()+": " +name+": "+dCurValue+", no change");
        }
        return diff;
    }

    protected List<ChangeableObject> subObjects() {
        return Collections.emptyList();
    }

    protected <T extends ChangeableObject> T ensureSubObject(T currentVal, Class<T> tClass, Consumer<T> setter) {

        if (currentVal == null) {
            T newItem = LibUtil.reThrow(tClass::newInstance);
            if (isOpen()) {
                setter.accept(newItem);
            }
            return newItem;
        }
        return currentVal;

    }

}
