package com.excelfore.esync.brp.util;

import static com.excelfore.esync.brp.Constants.TAG;

import android.os.Build;
import android.os.RemoteException;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Locale;
import java.util.concurrent.Callable;
import java.util.function.Consumer;
import java.util.function.Function;

public class LibUtil {

    /**
     * Extracts result from a {@link Callable}, but not throwing (and logging)
     * any exceptions that might've been thrown from executing the callable,
     * using an alternative function to get the result if an exception is thrown.
     * @param from get result from
     * @return result from Callable
     */
    public static <T> T noThrow(Callable<T> from, Function<Throwable, T> onThrow) {
        try {
            return from.call();
        } catch (Exception e) {
            L.e(TAG, "Unexpected ignored exception", e);
            return onThrow.apply(e);
        }
    }

    /**
     * Just ignores (but logs) any exceptions thrown from the supplied runnable.
     */
    public static void noThrow(RunnableT<? extends Throwable> from) {
        try {
            from.run();
        } catch (Throwable e) {
            L.e(TAG, "Unexpected ignored exception", e);
        }
    }

    /**
     * Executes a {@link RunnableT}, throwing any produced exception as
     * a runtime exception.
     * @param r Runnable to execute
     */
    public static void reThrow(RunnableT<? extends Throwable> r) {

        try {
            r.run();
        } catch (Throwable e) {
            throw doThrow(e);
        }

    }

    /**
     * Extracts result from a {@link Callable}, throwing any produced exception as
     * a runtime exception.
     * @param from get result from
     * @return result from Callable
     */
    public static <T> T reThrow(Callable<T> from) {
        try {
            return from.call();
        } catch (Exception e) {
            throw doThrow(e);
        }
    }

    /**
     * Enables to throw an exception as a run-time exception.
     * This method does not declare any thrown exceptions, but any exception
     * can be safely passed to it, so it is re-thrown to the caller as-is.
     * @param e exception to throw
     * @return thrown exception.
     */
    // Thanks to http://blog.jooq.org/2012/09/14/throw-checked-exceptions-like-runtime-exceptions-in-java/
    public static RuntimeException doThrow(@NonNull Throwable e) {
        return doThrow0(e);
    }
    @SuppressWarnings("unchecked")
    private static <E extends Throwable> RuntimeException doThrow0(@NonNull Throwable e) throws E {
        throw (E) e;
    }


    public static void copyIO(Reader r, Writer w) throws IOException {
        char [] buf = new char[16384];
        while (true) {
            int nr = r.read(buf);
            if (nr < 0) { return; }
            w.write(buf, 0, nr);
        }
    }

    public static void copyIO(InputStream r, OutputStream w) throws IOException {
        copyIO(r, w, null);
    }
    public static void copyIO(InputStream r, OutputStream w, Consumer<ByteBuffer> onData) throws IOException {
        byte [] buf = new byte[16384];
        while (true) {
            int nr = r.read(buf);
            if (nr < 0) { return; }
            if (onData != null) {
                onData.accept(ByteBuffer.wrap(buf, 0, nr));
            }
            w.write(buf, 0, nr);
        }
    }

    public static byte[] readFully(InputStream r) throws IOException {
        ByteArrayOutputStream o = new ByteArrayOutputStream();
        copyIO(r, o);
        return o.toByteArray();
    }

    public static <T> T remote(Callable<T> c) throws RemoteException {
        try {
            return c.call();
        } catch (Throwable e) {
            L.e(TAG, "Re-throwing as remote", e);
            throw new RemoteException(e.getMessage());
        }
    }

    public static String num(long v) {

        // use English, because not all locales have thousands separator
        return String.format(Locale.ENGLISH, "%,d", v);

    }

    public static boolean isUnderTest() {
        return "robolectric".equals(Build.FINGERPRINT);
    }

    public static String hexString(byte[] data) {

        return new String(hexChars(data));

    }

    public static char [] hexChars(byte[] data) {

        // Create Hex String
        char [] c = new char[data.length * 2];
        var i = 0;
        for (byte one : data) {

            Function<Byte, Character> half = b->{
                if (b > 9) { return (char)(b - 10 + 'a'); }
                return (char)(b + '0');
            };

            c[i++] = half.apply((byte)(((one >> 4)&0xf)));
            c[i++] = half.apply((byte)(one & 0xf));

        }

        return c;

    }

    /**
     * Returns hexadecimal MD5 value of bytes in specified string,
     * converted to bytes in {@link StandardCharsets#UTF_8} encoding.
     * @param s string to hash
     * @return hexadecimal hash
     */
    public static String hexMD5(@Nullable String s) {

        try {
            // Create MD5 Hash
            MessageDigest digest = MessageDigest.getInstance("MD5");

            if (s != null) {
                digest.update(s.getBytes(StandardCharsets.UTF_8));
            }

            byte[] messageDigest = digest.digest();

            return hexString(messageDigest);

        } catch (Throwable e) {
            L.e(TAG, "hashing failed?", e);
            return "<hash error>";
        }

    }

    /**
     * Turns empty string into a {@code null}, or returns a trimmed string.
     * Trimming is done using {@link String#trim()}.
     * @param s string to trim/nullify.
     * @return {@code null} if the input, once trimmed, is empty, or was {@code null} to begin
     * with; otherwise returns trimmed value of the input.
     */
    public static String sTrim(String s) {
        if (s == null) { return null; }
        s = s.trim();
        if (s.isEmpty()) { return null; }
        return s;
    }

}
