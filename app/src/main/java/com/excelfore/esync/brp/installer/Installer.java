package com.excelfore.esync.brp.installer;

import static com.excelfore.esync.brp.Constants.KSS_LOCATION;
import static com.excelfore.esync.brp.Constants.PL_CONFIGS_LOCATION;
import static com.excelfore.esync.brp.Constants.PL_FILES_LOCATION;
import static com.excelfore.esync.brp.Constants.TAG;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Handler;

import androidx.annotation.Nullable;

import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.SessionWork;
import com.excelfore.esync.brp.db.UpdateProgress;
import com.excelfore.esync.brp.model.sync_js.ClientMessage;
import com.excelfore.esync.brp.model.sync_js.ComponentState;
import com.excelfore.esync.brp.model.sync_js.WInstallation;
import com.excelfore.esync.brp.session.CurrentSession;
import com.excelfore.esync.brp.session.SessionRequest;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.google.common.util.concurrent.SettableFuture;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Objects;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class Installer extends SessionWork {

    private final SettableFuture<InstallResult> result;
    private BigDecimal progress;
    private final File downloadedFile;
    private UpdateContext uCtx;

    public Installer(CurrentSession session, BRPUpdateApp app, File downloadedFile) {
        super(app, session);
        this.downloadedFile = downloadedFile;
        result = SettableFuture.create();
    }

    public void install() {

        // code from UpdateHandlerSystem.processUpdate

        // let's see if we already applied the update, but we just need
        // the reboot to complete.

        if (fromProgress(up->{
            if (up.isUpdateApplied()) {
                L.i(TAG, "Update is already considered applied, nothing to do");
                result.set(InstallResult.success());
                return true;
            }
            return false;
        })) {
            return;
        }

        if (checkAborted(null)) {
            return;
        }

        // this will check for whether the campaign was aborted
        SettableFuture<Void> async = SettableFuture.create();
        app.performSyncSettings(this, ()->{ if (!checkAborted(async)) { irrevocableUpdate(async); } }, true);
        try {
            async.get();
        } catch (Exception e) {
            result.setException(e);
        }

    }

    private boolean checkAborted(@Nullable SettableFuture<?> release) {

        return fromUpdateState(us->{

            WInstallation target = us.getTarget();
            if (target == null) {
                RuntimeException e = new RuntimeException("No target object in installer?");
                if (release == null) { throw e; }
                release.setException(e);
                return true;
            }

            if (target.isAborted()) {
                InstallResult ir = new InstallResult();
                ir.setTerminal();
                ir.updateFailure(ClientMessage.F.CAMPAIGN_ABORTED.make(), new Exception("Installation record aborted"));
                result.set(ir);
                if (release != null) {
                    release.set(null);
                }
                return true;
            }

            return false;

        });


    }

    private File unzipTo(ZipInputStream zis, String name, File targetDir) throws IOException {

        File eFile = new File(name);
        File target = new File(targetDir, eFile.getName());
        L.i(TAG, "Unzipping "+name+" to "+target.getAbsolutePath());
        try (var fos = new FileOutputStream(target)) {
            LibUtil.copyIO(zis, fos);
        }
        Set<PosixFilePermission> allAccess = PosixFilePermissions.fromString("rw-rw-rw-");
        Files.setPosixFilePermissions(target.toPath(), allAccess);
        return target;

    }

    @SuppressLint("SetWorldReadable")
    private void irrevocableUpdate(SettableFuture<?> forException) {

        var mainLoop = new Handler(app.getMainLooper());

        try {

            // we have to count this as an installation attempt now.
            onProgress(up->{
                up.setAttempted(up.getAttempted()+1);
                up.setUpdateSubmitted(true);
            });


            Runnable r = () -> {

                try {

                    uCtx = new UpdateContext(this, downloadedFile);

                    uCtx.setReportProgressTo(v->onUpdateEngineUpdate(0, v));

                    submitWork("Sending to PackageInstaller", ()->{

                        var main = app.getMainActivity();

                        try {

                            main.showProgress();

                            onUpdateEngineUpdate(0, BigDecimal.ZERO);

                            var zis = uCtx.getZis();

                            File kss = new File(KSS_LOCATION);
                            File primary = new File(PL_FILES_LOCATION);
                            File configs = new File(PL_CONFIGS_LOCATION);

                            createDirectories(kss, primary, configs);

                            JsonObject config = null;
                            File mainOta = null;
                            File configFile = null;

                            while (true) {

                                ZipEntry ze = zis.getNextEntry();
                                if (ze == null) { break; }

                                var name = ze.getName();
                                if (name.startsWith("kss_bins")) {
                                    unzipTo(zis, name, kss);
                                } else if (name.startsWith("files")) {
                                    mainOta = unzipTo(zis, name, primary);
                                } else if (name.startsWith("configs")) {
                                    var cfg = unzipTo(zis, name, configs);
                                    if (name.endsWith("android-ota-full.json")) {
                                        configFile = cfg;
                                        try (var fr = Files.newBufferedReader(cfg.toPath(), StandardCharsets.UTF_8)) {
                                            config = JsonParser.parseReader(fr).getAsJsonObject();
                                        }
                                    }
                                } else {
                                    L.i(TAG, "Skipping "+name+", not a recognized file");
                                }

                            }

                            Objects.requireNonNull(config, "No config.json was found in the update binary");
                            Objects.requireNonNull(configFile, "No config.json was found in the update binary");
                            Objects.requireNonNull(mainOta, "No main OTA payload was found in the update binary");

                            config.addProperty("url", "file://"+mainOta.getAbsolutePath());
                            try (var wr = Files.newBufferedWriter(configFile.toPath(), StandardCharsets.UTF_8, StandardOpenOption.TRUNCATE_EXISTING)) {
                                wr.write(JsonThings.gsonNE().toJson(config));
                            }

                            onEnginePayloadApplicationComplete(null);

                            if (main.useAm()) {

                                /* -- this will probably not work. */
                                Process process = Runtime.getRuntime().exec("am start -a android.intent.action.SEND -t text/plain --es android.intent.extra.TEXT android-ota-full com.example.android.systemupdatersample");

                                dumpOut(process.getInputStream(), "stdout");
                                dumpOut(process.getErrorStream(), "stderr");

                                int rc = process.waitFor();
                                if (rc != 0) {
                                    throw new Exception("am finished with non-0 ec "+rc);
                                }

                            } else {

                                Intent sendIntent = new Intent();
                                sendIntent.setAction(Intent.ACTION_SEND);
                                sendIntent.setType("text/plain");
                                sendIntent.putExtra(Intent.EXTRA_TEXT, "android-ota-full");
                                sendIntent.putExtra("com.example.android.systemupdatersample.CONFIG", JsonThings.gsonNE().toJson(config));
                                sendIntent.setPackage("com.example.android.systemupdatersample");
                                main.startActivity(sendIntent);

                            }

                            L.i(TAG, "Intent sent");

                            app.getScheduler().scheduleNow(new SessionRequest("Update completed"));

                        } catch (Exception e) {
                            L.e(TAG, "APK installation session failed", e);
                            onEnginePayloadApplicationComplete(e.getMessage());
                        } finally {
                            main.hideProgress();
                            main.hideUpdate();
                        }

                    });

                } catch (Exception e) {

                    L.e(TAG, "Failed to initialize update", e);

                    // we are running on the main thread (because we need to be to launch
                    // the update manager), so we must run on an executor to report failure
                    submitWork("on update application exception", ()-> considerUpdateFailed(e.getMessage()));
                }

                app.setUpdating(true);
            };

            report(report->{
                report.setError(ClientMessage.F.UPDATE_ENGINE.make());
                report.setComponentState(ComponentState.UPDATE_PROGRESSING);
            });

            mainLoop.post(r);

            forException.set(null);

        } catch (Exception e) {
            forException.setException(e);
        }

    }

    private void dumpOut(InputStream stream, String name) {

        app.getMainExecutor().execute(()->{
            try {
                BufferedReader in = new BufferedReader(new InputStreamReader(stream));
                while (true) {
                    String s = in.readLine();
                    if (s == null) {
                        L.i(TAG, "-- "+name+" EOF from am");
                        break;
                    }
                    L.i(TAG, "am "+name+": " + s);
                }
            } catch (Exception e) {
                L.e(TAG, "Error reading "+name, e);
            }
        });

    }

    /**
     * Invoked when the update activity is completed.
     */
    private void onEnginePayloadApplicationComplete(String error) {

        app.getMainActivity().hideConsent();
        L.i(TAG, "Update application complete, failed=" + error);

        submitWork("onPayloadApplicationComplete", ()->{

            if (uCtx != null) {

                uCtx.close();
                uCtx = null;

            }

            if (error != null) {
                considerUpdateFailed(error);
            } else {
                considerUpdateApplied();
            }

            app.setUpdating(false);

        });

    }

    private void onUpdateEngineUpdate(int status, BigDecimal progress) {

        // L.i(TAG, "StatusUpdate - status=" + status + ", progress "+progress);

        if (progress == null) { return; }

        if (this.progress == null) {
            this.progress = progress;
        } else {
            this.progress = progress.max(this.progress);
        }

        reportUEProgress();

    }

    private void reportUEProgress() {

        var uCtx = this.uCtx;
        if (uCtx == null ) { return; }

        // main activity uses progress bar with the granularity of 10000, so that's we
        // that's we scale to.

        long p;
        var rp = this.progress;
        if (rp == null) { p = 0; } else { p = rp.multiply(BigDecimal.valueOf(10000)).longValue(); }

        // don't report errors from here, only report progress or "Finished"
        app.getMainActivity().setProgress(p);

    }

    private void considerUpdateFailed(String error) {
        result.set(InstallResult.error(ClientMessage.F.UPDATE_FAILED_UA.make(error)));
        onUpdateState(us->{
            us.getReport().setFailureCount(us.getProgress().getAttempted());
            us.getProgress().setUpdateSubmitted(false);
        });
    }

    private void considerUpdateApplied() {

        app.getOurExecutorService().submit(()->{

            try {

                onUpdateState(us->{
                    UpdateProgress up = us.getProgress();
                    up.setPrnShown(false);
                    up.setUpdateSubmitted(false);
                    up.setUpdateApplied(true);
                    us.getReport().setError(ClientMessage.F.REBOOT_REQUIRED.make());
                    // Setting state here for emulator, as emulator doesn't go through the
                    // flow where component state set to progressing
                });

                //app.getNotificationService().showRebootRequired();
                result.set(InstallResult.success());
            } catch (Exception e) {
                result.setException(e);
            }

        });

    }

    public SettableFuture<InstallResult> getResult() {
        return result;
    }

    private void createDirectories(File...fs) throws IOException {

        for (var f : fs) {
            var p = f.toPath();
            Files.createDirectories(p);
            Files.setPosixFilePermissions(p, PosixFilePermissions.fromString("rwxrwxrwx"));
        }

    }

}
