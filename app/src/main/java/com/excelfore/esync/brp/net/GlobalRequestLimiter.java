package com.excelfore.esync.brp.net;

import java.util.EnumMap;

public class GlobalRequestLimiter extends EnumMap<Endpoints, LimitedRequestRunState> {

    private boolean isBlocked;

    public GlobalRequestLimiter() {
        super(Endpoints.class);
    }

    public void controlBlockedState(boolean isBlocked) {
        this.isBlocked = isBlocked;
    }

    public boolean isBlocked() {
        return isBlocked;
    }

}
