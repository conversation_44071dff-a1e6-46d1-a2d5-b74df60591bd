package com.excelfore.esync.brp.util;

import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;

public class MeasuringInputStream extends FilterInputStream {

    protected long size;
    private final boolean closeSub;
    private boolean closed;

    public MeasuringInputStream(InputStream src, boolean closeSub) {
        super(src);
        this.closeSub = closeSub;
    }


    @Override
    public int read() throws IOException {
        int x = in.read();
        if (x >= 0) {
            measure(new byte[] {(byte)x}, 0, 1);
        }
        return x;
    }

    @Override
    public int read(byte [] b, int off, int len)
            throws IOException {

        int c = in.read(b, off, len);
        if (c > 0) {
            measure(b, off, c);
        }
        return c;

    }

    protected void measure(byte [] b, int off, int len) {
        size += len;
    }

    @Override
    public void close() throws IOException {

        if (closed) { return; }

        if (closeSub) {
            super.close();
        }

        closed = true;

    }

    public long getSize() {
        return size;
    }

}
