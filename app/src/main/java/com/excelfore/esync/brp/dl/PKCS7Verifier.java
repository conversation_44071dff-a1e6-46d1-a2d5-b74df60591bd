package com.excelfore.esync.brp.dl;

import android.util.Base64;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.CRLChecker;
import com.excelfore.esync.brp.client.SessionWork;
import com.excelfore.esync.brp.util.Crypto;
import com.excelfore.esync.brp.util.FunctionT;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.MyUtil;
import com.excelfore.esync.brp.util.RunnableT;
import com.google.common.io.ByteStreams;
import com.google.common.util.concurrent.SettableFuture;

import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.ASN1Set;
import org.bouncycastle.asn1.cms.ContentInfo;
import org.bouncycastle.asn1.cms.SignedData;
import org.bouncycastle.asn1.cms.SignerInfo;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cms.CMSProcessableByteArray;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.cms.SignerInformation;
import org.bouncycastle.cms.SignerInformationStore;
import org.bouncycastle.cms.SignerInformationVerifier;
import org.bouncycastle.cms.jcajce.JcaSimpleSignerInfoVerifierBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.Store;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.security.DigestInputStream;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CRL;
import java.security.cert.CertPathBuilder;
import java.security.cert.CertStore;
import java.security.cert.CertStoreParameters;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.CollectionCertStoreParameters;
import java.security.cert.PKIXBuilderParameters;
import java.security.cert.X509CertSelector;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class PKCS7Verifier {

    private final static String SIGNATURE = "xl4-signature.txt";
    private final static String SIGNATURE_PKCS7 = "xl4-signature.p7";
    private final static String HASH_ALGORITHM = "SHA-512";
    private final static String HASH_ALGORITHM_OID = "2.16.840.*********.2.3";
    private final static int MAX_SIGNATURE_SIZE = 65536;
    private final static String PKCS7_SIGNED_DATA_OID = "1.2.840.113549.1.7.2";

    public final SettableFuture<NegativeVerifierResult> result = SettableFuture.create();
    public final PipedOutputStream feeder = new PipedOutputStream();
    public final BRPUpdateApp app;
    private boolean started;
    private final AtomicBoolean connectLock = new AtomicBoolean();
    private final SessionWork updateHelper;

    public PKCS7Verifier(SessionWork ut) {
        this.app = ut.app;
        updateHelper = ut;
    }

    public void start() {

        started = true;

        updateHelper.submitWork("pkcs7 verifier", ()->{

            try (PipedInputStream pis = new PipedInputStream(feeder);
                    ZipInputStream zis = new ZipInputStream(pis)) {

                synchronized (connectLock) {
                    connectLock.set(true);
                    connectLock.notify();
                }

                byte [] signedFileData = null;
                byte [] pkcs7data = null;
                Map<String, byte[]> hashMap = new HashMap<>();

                while (true) {

                    ZipEntry ze = zis.getNextEntry();
                    if (ze == null) { break; }

                    if (SIGNATURE.equals(ze.getName())) {
                        signedFileData = readMax(zis);
                    } else if (SIGNATURE_PKCS7.equals(ze.getName())) {
                        pkcs7data = readMax(zis);
                    } else {
                        hashMap.put(ze.getName(), sha512(zis));
                    }

                }

                if (signedFileData == null) {
                    result.set(new NegativeVerifierResult(SIGNATURE+" file not found in package"));
                    return;
                }

                if (pkcs7data == null) {
                    result.set(new NegativeVerifierResult(SIGNATURE_PKCS7+" file not found in package"));
                }

                Map<String, byte[]> fileHashes = new HashMap<>();

                try (StringReader sr = new StringReader(new String(signedFileData, StandardCharsets.UTF_8));
                        BufferedReader br = new BufferedReader(sr)) {

                    FunctionT<BufferedReader, String, IOException> chomp = (r) -> {
                        while (true) {
                            String s = r.readLine();
                            if (s == null) {
                                return null;
                            }
                            s = MyUtil.sTrim(s);
                            if (s == null || s.charAt(0) == '#') {
                                continue;
                            }
                            return s;
                        }
                    };

                    String hashingAlg = chomp.apply(br);
                    if (!HASH_ALGORITHM_OID.equals(hashingAlg)) {
                        result.set(new NegativeVerifierResult("Unsupported hash algorithm "+hashingAlg));
                        return;
                    }

                    while (true) {
                        String s = chomp.apply(br);
                        if (s == null) {
                            break;
                        }
                        fileHashes.put(s, Base64.decode(chomp.apply(br), 0));
                    }

                    for (Map.Entry<String, byte[]> me : hashMap.entrySet()) {

                        String fName = me.getKey();

                        byte[] hashInSig = fileHashes.get(fName);
                        if (hashInSig == null) {
                            result.set(new NegativeVerifierResult("File "+fName+" from the package has no hash entry"));
                            return;
                        }

                        if (!Arrays.equals(hashInSig, me.getValue())) {
                            result.set(new NegativeVerifierResult("File "+fName+" in the package has hash "+
                                    Base64.encodeToString(me.getValue(), Base64.NO_WRAP) +
                                    ", but signature file has "+
                                    Base64.encodeToString(hashInSig, Base64.NO_WRAP)));
                            return;
                        }

                    }

                    for (String s : fileHashes.keySet()) {
                        if (hashMap.get(s) == null) {
                            result.set(new NegativeVerifierResult("File "+s+" listed in signature file is not present in the package"));
                            return;
                        }
                    }

                    verifyPKCS7(signedFileData, pkcs7data);

                    L.i(Constants.TAG, "PKCS7 verification successful");
                    result.set(null);

                }

            } catch (Throwable e) {
                L.e(Constants.TAG, "PKCS7 verification failure", e);
                // Request a simple retry if the certificate is not yet valid,
                // the device time is probably not yet set.
                boolean needRetry = e instanceof CertificateNotYetValidException;
                result.set(new NegativeVerifierResult(e.getMessage(), needRetry));
            } finally {
                try {
                    L.i(Constants.TAG, "Verifier finished with :"+result.get());
                } catch (Exception e) {
                    L.wtf(Constants.TAG, "Can not get verifier result after it's finished!", e);
                }
            }

        });

        synchronized (connectLock) {
            while (!connectLock.get()) {
                LibUtil.reThrow((RunnableT<Throwable>) connectLock::wait);
            }
        }

    }

    private void verifyPKCS7(byte[] contents, byte[] pkcs7block) throws Exception {

        ContentInfo signedDataASN;

        try (ASN1InputStream ais = new ASN1InputStream(new ByteArrayInputStream(pkcs7block))) {

            // this first check just checks that the signature has a good hash in it.
            // thanks to https://www.bouncycastle.org/devmailarchive/msg14355.html

            signedDataASN = ContentInfo.getInstance(ais.readObject());

            if (!PKCS7_SIGNED_DATA_OID.equals(signedDataASN.getContentType().getId())) {
                throw new Exception("Expected pkcs7 OID, got : " +
                        signedDataASN.getContentType().getId());
            }

            SignedData sd = SignedData.getInstance(signedDataASN.getContent());
            boolean hasGoodHash = false;

            ASN1Set var1 = sd.getSignerInfos();
            int l = var1.size();
            Collection<String> usedAlgorithms = new HashSet<>();
            for (int i = 0; i < l; i++) {

                SignerInfo sin = SignerInfo.getInstance(var1.getObjectAt(i));
                ASN1ObjectIdentifier oid = sin.getDigestAlgorithm().getAlgorithm();
                if (HASH_ALGORITHM_OID.equals(oid.getId())) {
                    hasGoodHash = true;
                    break;
                } else {
                    usedAlgorithms.add(oid.getId());
                }

            }

            if (!hasGoodHash) {
                throw new Exception("PKCS7 object does not have signed SHA-512 hash, found: " + String.join(",", usedAlgorithms));
            }
        }

        // now - full PKCS7 validation
        // special thanks to
        // http://stackoverflow.com/questions/20853157/combining-all-of-the-tasks-needed-to-verify-a-pkcs7-signature

        KeyStore anchors = app.getSignatureTrustStore();
        CRL crl = app.getCRL();

        BouncyCastleProvider bc = new BouncyCastleProvider();

        CMSSignedData s = new CMSSignedData(new CMSProcessableByteArray(contents), signedDataASN);

        Store<X509CertificateHolder> certStore = s.getCertificates();

        Collection<X509Certificate> x509CertStore = certStore.getMatches(null).stream().map(Crypto::convertFromHolder).collect(
                Collectors.toCollection(ArrayList::new));

        SignerInformationStore signers = s.getSignerInfos();

        Date valid = new Date();

        for (SignerInformation si : signers) {

            //noinspection unchecked
            Collection<X509CertificateHolder> certCollection = certStore.getMatches(si.getSID());
            Iterator<X509CertificateHolder> certIt = certCollection.iterator();
            X509CertificateHolder signerCertificateHolder = certIt.next();
            X509Certificate signerCertificate = Crypto.convertFromHolder(signerCertificateHolder);

            try {
                signerCertificate.checkValidity();
            } catch (CertificateExpiredException e) {
                // Don't wrap CertificateNotYetValidException, as we need to
                // catch it as is in the caller.
                throw new Exception("Binary signed with expired certificate", e);
            }

            X509CertSelector target = new X509CertSelector();

            target.setCertificate(signerCertificate);
            target.setCertificateValid(valid);

            PKIXBuilderParameters params = new PKIXBuilderParameters(anchors, target);
            CertStoreParameters intermediates = new CollectionCertStoreParameters(x509CertStore);
            params.addCertStore(CertStore.getInstance("Collection", intermediates, bc));
            CertPathBuilder builder = CertPathBuilder.getInstance("PKIX", bc);
            params.setRevocationEnabled(false); // we do our own CRL checking
            if (crl != null) {
                params.addCertPathChecker(new CRLChecker(crl));
            }
            builder.build(params);

            JcaSimpleSignerInfoVerifierBuilder svBuilder = new JcaSimpleSignerInfoVerifierBuilder();
            SignerInformationVerifier verifier = svBuilder.build(signerCertificateHolder);
            if (!si.verify(verifier)) {
                throw new Exception("Failed signature verification against certificate " + signerCertificateHolder.getSubject());
            }

        }

        // $TODO: We are not checking that the certificate contents agree to our requirements 😱

    }

    private byte[] readMax(ZipInputStream zis) throws IOException {

        int total = 0;
        byte [] buf = new byte[MAX_SIGNATURE_SIZE];
        byte [] result = new byte[0];
        while (true) {

            int nr = zis.read(buf);
            if (nr < 0) { break; }
            // currently total is the number of bytes in result that we care for.

            if (total > MAX_SIGNATURE_SIZE) { continue; }

            byte [] newResult = new byte[nr + total];
            System.arraycopy(result, 0, newResult, 0, total);
            System.arraycopy(buf, 0, newResult, total, nr);
            total += nr;
            result = newResult;

        }

        return result;

    }

    private byte [] sha512(ZipInputStream zis) throws IOException, NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(HASH_ALGORITHM);
        DigestInputStream dis = new DigestInputStream(zis, md);
        LibUtil.copyIO(dis, ByteStreams.nullOutputStream());
        return dis.getMessageDigest().digest();
    }

    public boolean isStarted() {
        return started;
    }
}
