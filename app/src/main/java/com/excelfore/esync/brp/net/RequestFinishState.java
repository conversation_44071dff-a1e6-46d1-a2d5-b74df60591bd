package com.excelfore.esync.brp.net;

/**
 * Indicates the state with which a request has finished in relationship to the consequences
 * this must bear on the possibility to making a new request.
 */
public enum RequestFinishState {

    /**
     * The request has finished successfully, or at least in such a manner that it should
     * be retried only when full request interval timer elapses.
     */
    FULL_COUNT,
    /**
     * The request has failed with an error, and the error is classified as such that
     * it should be re-attempted without a retry timeout, at least if the retry count is
     * not yet exhausted.
     */
    ERROR_COUNT,
    /**
     * The request has failed due to an external unresolved dependency of a sort. This generally
     * means that: <ul>
     *     <li>Session should not be retried right away</li>
     *     <li>If a session is retried, the request should be allowed</li>
     * </ul>
     * This response should influence the request limiters globally, and not just the one where
     * this problem occurred.
     */
    BLOCKED

}
