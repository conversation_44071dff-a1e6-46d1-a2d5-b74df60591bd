package com.excelfore.esync.brp.model;

import android.util.Base64;

import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.MyUtil;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;
import java.security.MessageDigest;
import java.util.Arrays;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class EncryptedBinaryValue {

    private final byte [] iv;
    private final byte [] data;
    private final String hash;

    public final static String P_IV = "iv";
    public final static String P_DATA = "data";
    public final static String P_HASH = "hash";

    private EncryptedBinaryValue(byte [] iv, byte [] data, String hash) {
        this.iv = iv;
        this.data = data;
        this.hash = hash;
    }

    private EncryptedBinaryValue(JsonElement json) {
        if (json.isJsonObject()) {
            this.iv = Base64.decode(json.getAsJsonObject().get(P_IV).getAsString(), Base64.NO_WRAP);
            this.data = Base64.decode(json.getAsJsonObject().get(P_DATA).getAsString(), Base64.NO_WRAP);
            // if loaded from older client DB, hash may not be available.
            JsonElement hashEl = json.getAsJsonObject().get(P_HASH);
            if (hashEl != null) {
                this.hash = hashEl.getAsString();
            } else {
                this.hash = null;
            }
        } else {
            throw new RuntimeException("Can't parse encrypted string, got "+json);
        }
    }

    public byte [] getValue(BRPUpdateApp app) {

        return LibUtil.reThrow(()->{
            byte [] key = app.getSharedEncryptionKey();
            Cipher cipher = Cipher.getInstance("AES/CTR/NoPadding");
            SecretKeySpec skc = new SecretKeySpec(key, cipher.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, skc, new IvParameterSpec(iv));
            byte [] result = cipher.doFinal(data);
            Arrays.fill(key, (byte)0);
            return result;
        });

    }

    public static class SD implements JsonSerializer<EncryptedBinaryValue>, JsonDeserializer<EncryptedBinaryValue> {

        private final BRPUpdateApp app;

        public SD(BRPUpdateApp app) {
            this.app = app;
        }

        @Override
        public EncryptedBinaryValue deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {

            return LibUtil.reThrow(()->{

                if (json.isJsonNull()) { return null; }
                if (json.isJsonPrimitive() && app != null) {
                    // the value is not encrypted, probably received through a trusted channel.

                    byte [] pt = Base64.decode(json.getAsString(), Base64.NO_WRAP);

                    byte [] iv = new byte[16];
                    MyUtil.secureRandom.nextBytes(iv);
                    byte [] key = app.getSharedEncryptionKey();

                    Cipher cipher = Cipher.getInstance("AES/CTR/NoPadding");
                    SecretKeySpec skc = new SecretKeySpec(key, cipher.getAlgorithm());
                    cipher.init(Cipher.ENCRYPT_MODE, skc, new IvParameterSpec(iv));

                    byte [] ct = cipher.doFinal(pt);

                    MessageDigest md = MessageDigest.getInstance("SHA-256");
                    String hash = Base64.encodeToString(md.digest(pt), Base64.NO_WRAP);

                    Arrays.fill(pt, (byte)0);
                    Arrays.fill(key, (byte)0);

                    return new EncryptedBinaryValue(iv, ct, hash);

                }

                return new EncryptedBinaryValue(json);

            });
        }

        @Override
        public JsonElement serialize(EncryptedBinaryValue src, Type typeOfSrc, JsonSerializationContext context) {

            return LibUtil.reThrow(()->{
                if (src == null) {
                    return JsonNull.INSTANCE;
                }

                JsonObject obj = new JsonObject();
                obj.addProperty(P_IV, src.getIv());
                obj.addProperty(P_DATA, src.getData());
                obj.addProperty(P_HASH, src.hash);

                return obj;

            });

        }
    }

    public String getIv() {
        return Base64.encodeToString(iv, Base64.NO_WRAP);
    }

    public String getData() {
        return Base64.encodeToString(data, Base64.NO_WRAP);
    }

    public String getHash() {
        return hash;
    }
}
