package com.excelfore.esync.brp.test;


import com.excelfore.esync.brp.model.sync_js.WInstallationRecord;

import java.util.List;

/**
 * Represents data available to the reporter at the end of the cycle.
 * There are generally three possibilities that this data may represent:
 * <ul>
 *     <li>Reporter needed to wait, because the web-service request could not yet be
 *     executed, or because sleep was forced. In this case, {@link #wait} will be a positive
 *     value, and {@link #toSend} will be {@code null}.
 *     </li>
 *     <li>Reporter has data to send, and can send it. In this case, {@link #wait} will
 *     be {@code null}, and {@link #toSend} will be a not empty list. Note that in this case,
 *     whatever data that needed to be sent, has been attempted to be sent before
 *     {@link ManualReportControl#nextCycle(Long, List)} is called, and the result of that
 *     operation could be read by calling {@link ManualReportControl#waitForCycle()}. The report
 *     queue will be up-to-date at that point in time, which means that if sending was successful,
 *     the queue should be empty (unless additional items were added to the queue after reporter
 *     retrieved the list of queue elements to send out).
 *     </li>
 *     <li>Reporter has no data to send, but could send it. In this case {@link #wait} will
 *     be {@code null}, and {@link #toSend} will be {@code null} or an empty list.
 *     </li>
 * </ul>
 */
public class ReporterCycleData {

    /**
     * Wait time reporter was to sleep for at the end of the cycle.
     * {@code null} indicates that no sleep was needed.
     */
    public final Long wait;

    /**
     * Data that the reporter needed to send out. Only populated if
     * {@link #wait} is {@code null}. Can still be {@code null} or empty,
     * if there was no actual data to send out.
     */
    public final List<WInstallationRecord> toSend;

    public ReporterCycleData(Long wait, List<WInstallationRecord> toSend) {
        this.wait = wait;
        this.toSend = toSend;
    }

    /**
     * Indicates whether the reporter sent any data in the corresponding cycle (whether because
     * there was no data to send, or wait was required instead).
     * @return {@code true} if data was NOT sent, {@code false} otherwise.
     */
    public boolean isEmpty() {

        return toSend == null || toSend.isEmpty();

    }
}
