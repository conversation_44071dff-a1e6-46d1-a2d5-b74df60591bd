package com.excelfore.esync.brp.util;

import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.OutputStream;

public class MeasuringOutputStream extends FilterOutputStream {

    private final byte [] one = new byte[1];
    protected boolean closed;
    private long size;

    public MeasuringOutputStream(OutputStream dst) {
        super(dst);
    }

    @Override
    public void write(int b) throws IOException {
        out.write(b);
        byte [] one = this.one;
        one[0] = (byte)b;
        updateBytes(one, 0, 1);
    }

    @Override
    public void write(byte [] b) throws IOException {
        // NOTE: never call super.write() here
        out.write(b);
        updateBytes(b, 0, b.length);
    }

    @Override
    public void write(byte [] b, int off, int len) throws IOException {
        // NOTE: never call super.write() here
        out.write(b, off, len);
        updateBytes(b, off, len);
    }

    protected void updateBytes(byte[] b, int off, int len) {
        size += len;
    }

    @Override
    public void close() throws IOException {
        if (closed) { return; }
        closed = true;
        super.close();
    }

    /**
     * Returns the number of bytes that were funnelled through this stream.
     * @return number of bytes funnelled through this stream.
     */
    public long getSize() {
        return size;
    }
}
