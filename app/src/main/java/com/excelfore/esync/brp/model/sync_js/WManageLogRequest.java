package com.excelfore.esync.brp.model.sync_js;

public class WManageLogRequest {

    // Command on the specified log file
    private WLogManagementCommand command;

    // The ID of the log entry, to be used for upload purposes.
    private Long id;
    
    // The log file that the command is for
    private String name;

    private String sha256;

    public WLogManagementCommand getCommand() {
        return command;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getSha256() {
        return sha256;
    }
}
