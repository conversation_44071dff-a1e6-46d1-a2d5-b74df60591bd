package com.excelfore.esync.brp.util;

import android.util.SparseArray;
import android.util.Xml;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.excelfore.esync.brp.Constants;

import org.bouncycastle.util.Arrays;
import org.json.JSONArray;
import org.json.JSONObject;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

// mostly copied from com.excelfore.api.lib.util.ApiUtil in appshack
public class MyUtil {

    public final static SecureRandom secureRandom = new SecureRandom();

    public static <S> void whenNotNull(@Nullable S val, @NonNull ConsumerT<S, Exception> fun) {
        whenNotNullOr(val, fun, null);
    }

    public static <S> void whenNotNull(@Nullable S val, @NonNull ConsumerT<S, Exception> fun, @Nullable SupplierT<S, Exception> whenNull) {
        LibUtil.reThrow(()->{
            S v = val;
            if (v == null) {
                if (whenNull != null) {
                    v = whenNull.get();
                }
            }
            if (v != null) {
                fun.accept(v);
            }
        });
    }

    public static <S> void whenNotNullOr(@Nullable S val, @Nullable ConsumerT<S, Exception> fun, @Nullable RunnableT<Exception> whenNull) {
        LibUtil.reThrow(()->{
            if (val == null) {
                if (whenNull != null) {
                    whenNull.run();
                }
            } else {
                if (fun != null) {
                    fun.accept(val);
                }
            }
        });
    }

    public static String versionForDisplay(String installVersion) {
        return versionForDisplay(installVersion, false);
    }

    public static String versionForDisplay(String installVersion, boolean quiet) {
        String buildVersion;
        String[] arrOfStr = installVersion.split(":");
        if (arrOfStr.length > 1) {
            String[] arrOfStrBuildVersion = arrOfStr[1].split("/");
            if (arrOfStrBuildVersion.length > 2) {
                buildVersion = arrOfStrBuildVersion[2];
            } else {
                buildVersion = arrOfStr[1];
            }
        } else {
            buildVersion = installVersion;
        }
        if (!quiet) {
            L.d(Constants.TAG, "version " + installVersion + " to be displayed as " + buildVersion);
        }
        return buildVersion;
    }

    public static Reader fileReader(String s) throws IOException {
        return fileReader(new File(s));
    }

    // we do this, because we can't guarantee that the files are stored in UTF-8
    // encoding by default. The default encoding may not be universal, and we store/read
    // in it, we may lose data.
    public static Reader fileReader(File f) throws IOException {
        return new InputStreamReader(new FileInputStream(f), StandardCharsets.UTF_8);
    }

    // we do this, because we can't guarantee that the files are stored in UTF-8
    // encoding.
    public static Writer fileWriter(File f) throws IOException {
        return new OutputStreamWriter(new FileOutputStream(f), StandardCharsets.UTF_8);
    }

    public static String readFully(Reader r) throws IOException {
        try (StringWriter sw = new StringWriter()) {
            LibUtil.copyIO(r, sw);
            return sw.toString();
        }
    }

    public static String readDMTreeNodes(Path path) {

        Path valFile = path.resolve("value");
        if (!Files.exists(valFile)) {
            L.d(Constants.TAG, "valFile not exist:  " + valFile);
            return null;
        }

        try {
            return new String(Files.readAllBytes(valFile));
        } catch (IOException e) {
            L.e(Constants.TAG, "Can't read "+valFile+", ignoring", e);
            return null;
        }
   }

    @NonNull
    public static byte[] readFullySecure(InputStream r) throws IOException {

        byte [] outputArray = new byte[0];
        byte [] buf = new byte[16384];

        while (true) {
            int nr = r.read(buf);
            if (nr < 0) { return outputArray; }
            byte [] newOutput = new byte[outputArray.length + nr];
            System.arraycopy(outputArray, 0, newOutput, 0, outputArray.length);
            System.arraycopy(buf, 0, newOutput, outputArray.length, nr);
            Arrays.fill(outputArray, (byte)0);
            Arrays.fill(buf, 0, nr, (byte)0);
            outputArray = newOutput;
        }

    }

    /**
     * This method trims the specified string, and returns NULL if the string
     * was {@code null}, or empty.
     *
     * @param s string to trim
     * @return trimmed string, or {@code null}
     */
    @Nullable
    public static String sTrim(String s) {
        if (s == null || "".equals(s = s.trim())) {
            return null;
        }
        return s;
    }

    public static Cipher getCipher(byte [] key, String encryptionMethod) {

        Cipher cipher = LibUtil.reThrow(()->Cipher.getInstance(encryptionMethod));
        SecretKeySpec skc = new SecretKeySpec(key, cipher.getAlgorithm());
        Arrays.fill(key, (byte)0);
        LibUtil.reThrow(()->cipher.init(Cipher.DECRYPT_MODE, skc, new IvParameterSpec(new byte[16])));
        return cipher;

    }


    public static void removeDirectoryRecursively(String dir, boolean withTop, Set<String> exclusion) {

        if (!new File(dir).exists()) {
            return;
        }

        try {

            // https://www.baeldung.com/java-delete-directory
            LinkedList<File> allFiles = new LinkedList<>();
            Files.walk(Paths.get(dir))
                    // .peek(System.out::println)
                    .map(Path::toFile)
                    .forEach(allFiles::add);

            allFiles.sort(Comparator.reverseOrder());

            if (!withTop) {
                allFiles.removeLast();
            }

            for (File f : allFiles) {
                if (exclusion != null && exclusion.contains(f.getAbsolutePath())) {
                    L.i(Constants.TAG,"Excluding "+f.getAbsolutePath()+" from deletion");
                } else {
                    if (!f.delete()) {
                        L.e(Constants.TAG, "Could not delete " + f.getAbsolutePath());
                    } else {
                        L.d(Constants.TAG, "Deleted " + f.getAbsolutePath());
                    }
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("Can not delete " + dir, e);
        }

        if (withTop) {
            if (new File(dir).exists()) {
                throw new RuntimeException("Failed to delete directory " + dir + ", still exists");
            }
        }

    }

    public static String getTagFromXML(String xmlFile, String tag) {
        XmlPullParser parser = Xml.newPullParser();
        String value = null;
        try {
            parser.setInput(new FileInputStream(xmlFile), null);
            while (parser.next() != XmlPullParser.END_DOCUMENT) {
                if (parser.getEventType() == XmlPullParser.START_TAG) {
                    if (parser.getName().equalsIgnoreCase(tag)) {
                        value = parser.nextText();
                        break;
                    }
                }
            }
            return value;
        } catch (IOException | XmlPullParserException e) {
            L.e(Constants.TAG, "XML Parse Error", e);
            return null;
        }
    }

    private static void ensureDirectory(String path) {
        ensureDirectory(new File(path));
    }
    private static void ensureDirectory(File target) {
        if (!target.exists()) {
            if (!target.mkdirs()) {
                L.w(Constants.TAG, "Could not crete all directories to "+target.getAbsolutePath());
            }
        }
    }

    public static void unZip(String zipFile, String location) throws IOException {

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry;

            ensureDirectory(location);

            while ((entry = zis.getNextEntry()) != null) {
                File outFile = Paths.get(location, entry.getName()).toFile();
                if (entry.isDirectory()) {
                    ensureDirectory(outFile);
                } else {
                    whenNotNull(outFile.getParentFile(), MyUtil::ensureDirectory);
                    try (FileOutputStream fos = new FileOutputStream(outFile)) {
                        LibUtil.copyIO(zis, fos);
                    }
                }
            }
        }
    }

    @Nullable
    public static <S, T> S ifNotNull(@Nullable T val, @NonNull FunctionT<T, S, Exception> fun, @Nullable SupplierT<S, Exception> whenNull) {
        try {
            if (val == null) {
                if (whenNull == null) { return null; }
                return whenNull.get();
            }
            return fun.apply(val);
        } catch (Exception e) {
            throw LibUtil.doThrow(e);
        }
    }

    @NonNull
    public static <S, T> S makeNotNull(@Nullable T val, @NonNull FunctionT<T, S, Exception> fun, @NonNull SupplierT<S, Exception> whenNull) {
        S ret = ifNotNull(val, fun, whenNull);
        if (ret == null) { throw new NullPointerException("not null must be produced"); }
        return ret;
    }

    @Nullable
    public static <S, T> S ifNotNull(@Nullable T val, @NonNull FunctionT<T, S, Exception> fun) {
        return ifNotNull(val, fun, null);
    }

    public static <T, S> S with(T input, Function<T, S> fun) {
        return fun.apply(input);
    }

    /**
     * Maps all public static final fields of a class (and its parent(s)) to a map, useful
     * for reversing constant names.
     * @param c class to evaluate
     * @return map of all integer constants (public static final)
     * declared in the class or its parents.
     */
    public static SparseArray<String> mapIntegerConstants(Class<?> c) {

        SparseArray<String> result = new SparseArray<>();

        for (Field f : c.getFields()) {

            int mod = f.getModifiers();
            // at least under Robolectric, the fields are not final. To protect ourselves from
            // picking up "wrong" constants (which could mess up the map), we only accept names
            // that have capital chars and underscores in them.
            if (!Modifier.isStatic(mod) /*|| !Modifier.isFinal(mod) */) {
                continue;
            }

            if (!Objects.equals(f.getType(), int.class) &&
                    !Objects.equals(f.getType(), Integer.class)) {
                continue;
            }

            String name = f.getName();
            boolean nameOk = true;
            for (char ch : name.toCharArray()) {
                if (Character.isUpperCase(ch)) { continue; }
                if (ch == '_') { continue; }
                nameOk = false;
                break;
            }
            if (!nameOk) { continue; }

            result.put((int)LibUtil.reThrow(()->f.get(null)), name);

        }

        return result;

    }
    /**
     * Represents instances of release note string content in a log-friendly way.
     * The release contents themselves are not included in the output, or the MD5 hash of the
     * entire string, the list of versions included in the release notes object, and the sizes
     * of the contents per string.
     * @param s release notes object to represent.
     * @return string representation of the release notes.
     */
    @NonNull
    public static String rnToString(@Nullable String s) {

        StringBuilder sb = new StringBuilder(LibUtil.hexMD5(s));
        if (s != null) {
            sb.append("(").append(s.length()).append("b: ");
            sb.append(briefLook(s)).append(")");
        } else {
            sb.append("(null)");
        }
        return sb.toString();

    }

    private static String briefLook(String s) {

        if (s == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        try {

            // Release Notes structure:
            // {
            //     "history": [
            //         {
            //             "version": 150,
            //             "introtext": {
            //                 "en": ...
            //                 ...
            //             },
            //             "components": [
            //                 {
            //                     "name": {
            //                         "en": "SDS",
            //                         ...
            //                     },
            //                     "introtext": {},
            //                     "changes": [],
            //                 },
            //                 ...
            //             ]
            //         },
            //         ...
            //     ]
            // }

            JSONObject jsonObject = new JSONObject(s);
            JSONArray historyArray = jsonObject.getJSONArray("history");

            int l = historyArray.length();
            if (l == 0) {
                sb.append("NO-HISTORY");
            }

            for (int i = 0; i < l; i++) {
                JSONObject historyObj = historyArray.getJSONObject(i);
                long version = historyObj.getLong("version");
                if (i > 0) {
                    sb.append(',');
                }
                sb.append(version);
            }

        } catch (Exception e) {
            L.e(Constants.TAG, "Failed to parse release note content: " + s, e);
            return "<BAD-JSON>";
        }

        return sb.toString();


    }

}
