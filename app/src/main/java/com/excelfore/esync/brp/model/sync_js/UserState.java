package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.model.NumericOMAEnum;
import com.excelfore.esync.brp.model.OMAEnumSD;
import com.google.gson.annotations.JsonAdapter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@JsonAdapter(OMAEnumSD.class)
public enum UserState implements NumericOMAEnum {

    CONFIRMED(0),
    NO_ACTION(1),
    DENIED(2),
    CONFIRMED_EXPIRED(3),
    POSTPONED(4),
    POSTPONED_EXPIRED(5),
    BLANKET(6),
    POLICY(7)
    ;

    public final int omaCode;

    private final static Map<Integer, UserState> byOmaCode =
            new ConcurrentHashMap<>();

    static {
        for (UserState ps : UserState.values()) {
            byOmaCode.put(ps.omaCode, ps);
        }
    }

    UserState(int omaCode) {
        this.omaCode = omaCode;
    }

    @Override
    public int getOmaCode() {
        return omaCode;
    }

    public static UserState getByOmaCode(int omaCode) {
        return byOmaCode.get(omaCode);
    }

}
