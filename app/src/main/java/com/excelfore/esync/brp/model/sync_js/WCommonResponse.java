package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.util.RFC3339;

public class WCommonResponse {

    private WPendingAbort pendingAbort;
    private WClientConfig config;
    // Current device log request, known to the server
    private WDeviceLogRequest logRequest;
    // Current log management request
    private WManageLogsRequest logManageRequest;
    // Server time, at the moment the response is formed.
    private RFC3339 time;

    public WPendingAbort getPendingAbort() {
        if (pendingAbort == null) {
            return new WPendingAbort();
        }
        return pendingAbort;
    }

    public WCommonResponse setPendingAbort(WPendingAbort pendingAbort) {
        this.pendingAbort = pendingAbort;
        return this;
    }

    public WClientConfig getConfig() {
        return config;
    }

    public WCommonResponse setConfig(WClientConfig config) {
        this.config = config;
        return this;
    }

    public WDeviceLogRequest getLogRequest() {
        return logRequest;
    }

    public WManageLogsRequest getLogManageRequest() {
        return logManageRequest;
    }

    public RFC3339 getTime() {
        return time;
    }
}
