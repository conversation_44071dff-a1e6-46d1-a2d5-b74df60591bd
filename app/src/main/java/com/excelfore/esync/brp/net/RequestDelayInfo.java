package com.excelfore.esync.brp.net;

import android.os.SystemClock;

/**
 * Represents information about delaying the request, or enabling to run it
 * immediately.
 */
public class RequestDelayInfo {

    /**
     * Duration, in milliseconds, until the request is allowed to be executed again,
     * {@code 0} indicating that the request can be executed now.
     */
    public final long delayMs;

    /**
     * Absolute time when the request can be executed, can be in the past. The time is measured
     * in milliseconds since boot, as returned by {@link SystemClock#elapsedRealtime()}.
     */
    public final long startMs;

    /**
     * Debugging information that is useful to determine how the next request time has been
     * computed.
     */
    public final String explanation;

    public RequestDelayInfo(long delayMs, long startMs, String explanation) {
        this.delayMs = delayMs;
        this.startMs = startMs;
        this.explanation = explanation;
    }
}
