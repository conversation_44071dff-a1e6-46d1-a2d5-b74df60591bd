package com.excelfore.esync.brp.db;

import android.database.Cursor;
import android.database.sqlite.SQLiteAbortException;
import android.database.sqlite.SQLiteAccessPermException;
import android.database.sqlite.SQLiteBlobTooBigException;
import android.database.sqlite.SQLiteCantOpenDatabaseException;
import android.database.sqlite.SQLiteDatabaseLockedException;
import android.database.sqlite.SQLiteDiskIOException;
import android.database.sqlite.SQLiteDoneException;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteFullException;
import android.database.sqlite.SQLiteMisuseException;
import android.database.sqlite.SQLiteOutOfMemoryException;
import android.database.sqlite.SQLiteReadOnlyDatabaseException;
import android.database.sqlite.SQLiteTableLockedException;
import android.os.SystemClock;

import androidx.annotation.NonNull;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.sqlite.db.SimpleSQLiteQuery;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.test.TL;
import com.excelfore.esync.brp.test.TestRequirements;
import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.FunctionT;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.net.NetUtils;
import com.google.gson.Gson;

import org.jetbrains.annotations.TestOnly;

import java.nio.file.Files;
import java.time.Duration;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.function.Supplier;

public class ESyncDatabaseWrapper {

    private final Lock databaseRLock = new ReentrantLock();

    private Thread updateStateHolder;
    private final Object updateStateLock = new Object();
    private final BRPUpdateApp app;

    private ESyncDatabase database = null;

    private void buildDB() {

        if (database != null) {
            return;
        }

        L.i(Constants.TAG, "Building database...");

        RoomDatabase.Builder<ESyncDatabase> dbBuilder =
                Room.databaseBuilder(this.app, ESyncDatabase.class, ESyncDatabase.NAME);

        Executor exe = TL.config.getRoomsExecutor();

        if (exe != null) {
            dbBuilder.setQueryExecutor(exe);
            dbBuilder.setTransactionExecutor(exe);
        }

        if (!LibUtil.isUnderTest() || !TestRequirements.foreGoMigration) {
            L.i(Constants.TAG, "Migrating the database");
        } else {
            L.w(Constants.TAG, "Database migration administratively disabled");
        }

        database = dbBuilder
                .fallbackToDestructiveMigration()
                .build();

        L.v(Constants.TAG, "Database built");

        try (Cursor c = database.query("select 1", null)) {
            c.moveToNext();
            c.getLong(0);
        }

        L.v(Constants.TAG, "Database verified");

    }
    
    private void _closeDB() {
        if (database != null) {
            L.i(Constants.TAG, "Close Database...");
            database.close();
            database = null;
        }
    }

    public void doCleanUp() {
        _closeDB();
    }

    private void resetDB() {
        L.i(Constants.TAG, "Reset Database...");
        _closeDB();
        try {
            Files.delete(app.getDatabasePath(ESyncDatabase.NAME).toPath());
        } catch (Exception e) {
            L.e(Constants.TAG, "Failed to delete DB file during reset", e);
        }
        buildDB();
    }

    public ESyncDatabaseWrapper(BRPUpdateApp app) {
        this.app = app;
    }

    // Only use for testing
    @TestOnly
    public ESyncDatabase getDBUnsafe() {
        return database;
    }

    protected <T> T locked(OnDB<T> op, Supplier<T> action) {

        String opName = op.name+"(" + (op.write?"W":"R") + ")";

        L.v(Constants.TAG, "Locking database ("+opName+")");

        try {
            if (!databaseRLock.tryLock(30, TimeUnit.SECONDS)) {
                throw new RuntimeException("Could not acquire database lock for "+opName+" within 30 seconds. Deadlock?");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            L.v(Constants.TAG, "Database locked ("+opName+")");
            return action.get();
        } finally {
            L.v(Constants.TAG, "Unlocking database ("+opName+")");
            databaseRLock.unlock();
        }

    }

    private void doFlush() {
        // https://stackoverflow.com/q/53724204/622266
        // https://stackoverflow.com/a/52089040/622266

        SupportSQLiteDatabase db = database.getOpenHelper().getWritableDatabase();

        try {

            try (Cursor c = db.query(new SimpleSQLiteQuery("pragma wal_checkpoint(full)"))) {
                if (c.moveToFirst() && c.getInt(0) == 1) {
                    throw new RuntimeException("Checkpoint was blocked from completing.");
                }
            }

        } catch (Exception e) {
            L.e(Constants.TAG, "Failed to flush DB!", e);
        }

    }

    private <T> T ward(OnDB<T> op) {
        return ward(op, null);
    }

    private <T> T ward(OnDB<T> op, Function<Exception, T> onError) {

        return locked(op, ()->{

            boolean retry = op.write;

            while (true) {

                try {

                    if (database == null || !database.isOpen()) {
                        // at least prevent NPEs and "DB closed" from resetting our DB.
                        database = null;
                        buildDB();
                    }

                    // IMPORTANT! We can't use transactions ourselves here,
                    // because the Rooms database code for inserts and removes
                    // already runs within a transaction, attempting to create
                    // a nest transaction will not work, and cause an effective
                    // deadlock. This is really silly, and forces us into an
                    // effectively single operation per transaction model.

                    if (op.write) {

                        T r = database.runInTransaction(()->op.op.apply(database));
                        doFlush();
                        return r;

                    } else {

                        return op.op.apply(database);

                    }

                } catch (SQLiteException e) {
                    L.wtf(Constants.TAG, "Failed " + op.name, e);

                    // this is a really serious problem for us, because we pretty
                    // much depend on the database contents. The best we can do - is to throw the
                    // database away, in some hope that we clear the whatever problem that has
                    // caused this.

                    if (!ignoreException(e)) {
                        resetDB();
                    }

                    if (retry) {
                        retry = false;
                        continue;
                    }
                    if (onError != null) {
                        return onError.apply(e);
                    }
                    return null;
                } catch (IllegalStateException e) {
                    L.wtf(Constants.TAG, "Failed " + op.name, e);
                    // java.lang.IllegalStateException: Migration didn't properly handle
                    // the database.
                    // This exception appears after adding MIGRATION_2_3
                    resetDB();
                    if (retry) {
                        retry = false;
                        continue;
                    }
                    if (onError != null) {
                        return onError.apply(e);
                    }
                    return null;
                } catch (Exception e) {
                    throw LibUtil.doThrow(e);
                }
            }

        });

    }

    @SuppressWarnings("RedundantIfStatement")
    private boolean ignoreException(SQLiteException e) {


        // Filter out known SQLiteException which doesn't need to reset database.
        // List of known SQLiteException:
        // https://developer.android.com/reference/android/database/sqlite/SQLiteException
        // Beside of these known exceptions, other database exceptions will be thrown as SQLiteException.

        if (e instanceof SQLiteAbortException ||
                e instanceof SQLiteAccessPermException ||
                //e instanceof SQLiteBindOrColumnIndexOutOfRangeException ||
                e instanceof SQLiteBlobTooBigException ||
                e instanceof SQLiteCantOpenDatabaseException ||
                //e instanceof SQLiteConstraintException ||
                //e instanceof SQLiteDatabaseCorruptException ||
                e instanceof SQLiteDatabaseLockedException ||
                //e instanceof SQLiteDatatypeMismatchException ||
                e instanceof SQLiteDiskIOException ||
                e instanceof SQLiteDoneException ||
                e instanceof SQLiteFullException ||
                e instanceof SQLiteMisuseException ||
                e instanceof SQLiteOutOfMemoryException ||
                e instanceof SQLiteReadOnlyDatabaseException ||
                e instanceof SQLiteTableLockedException) {
            // don't reset database for these exceptions
            return true;
        }

        return false;

    }

    @TestOnly
    public void simulateException(int c) {
        ConsumerT<ESyncDatabase, Exception> op = db->db.simulateException(c);
        ward(new OnDB<Void>("By Exception", db->{
            op.accept(db);
            return null;
        }), null);
    }

    public Settings loadSettings() {
        return ward(new OnDB<>("Loading Settings", ESyncDatabase::loadSettings), e-> Settings.makeDefault());
    }

    public void saveSettings(Settings settings) {

        ward(OnDB.writeOnly("Saving settings", db->db.saveSettings(settings)));
        app.updateSettings(settings);

    }

    @NonNull
    public UpdateState loadUpdateState(Gson gson) {

        String contents = ward(new OnDB<>("Loading update state", ESyncDatabase::loadUpdateState), e->null);
        if (contents == null) {
            UpdateState us = new UpdateState();
            saveUpdateState(gson.toJson(us)); // to make sure we synced it.
            return us;
        }

        return gson.fromJson(contents, UpdateState.class);

    }

    private void saveUpdateState(String state) {
        ward(OnDB.writeOnly("Saving update state", db->db.saveUpdateState(state)));
    }

    private UpdateState checkOutUpdateState() {

        synchronized (updateStateLock) {
            long now = SystemClock.elapsedRealtime();
            long maxWait = now + Duration.ofSeconds(30).toMillis();
            boolean waited = false;
            while (updateStateHolder != null) {

                if (updateStateHolder == Thread.currentThread()) {
                    throw new IllegalStateException("Attempt to check out the update state on the thread that already checked it out. Please instead submit the work that needs nested update state modification asynchronously");
                }

                waited = true;
                L.d(Constants.TAG, "Update state locked, waiting");
                try {
                    updateStateLock.wait(1000);
                } catch (InterruptedException ignored) {}
                if (SystemClock.elapsedRealtime() > maxWait) {
                    // NOTE: this won't work in Robolectric as it doesn't advance
                    // elapsedRealtime() unless asked to explicitly
                    throw new IllegalStateException("Update state could not be acquired in 30 seconds. Deadlock? Lock owner: "+
                            updateStateHolder.getName()+":"+updateStateHolder.getId());
                }
            }
            updateStateHolder = Thread.currentThread();
            if (waited) {
                L.d(Constants.TAG, "Spent "+
                        NetUtils.gTime(SystemClock.elapsedRealtime()-now)+
                        "ms waiting for update state");
            }

            UpdateState us = loadUpdateState(JsonThings.gsonNE());
            us.open();
            return us;

        }

    }

    private void releaseUpdateState(UpdateState s) {
        synchronized (updateStateLock) {
            updateStateLock.notifyAll();
            updateStateHolder = null;
            s.close();
        }
    }

    private void checkUpdateState(UpdateState updateState) {
        if (updateState.isChanged() || updateState.isStateSequenceChanged()) {

            if (updateState.isChanged()) {
                updateState.increaseSequence();
                app.getReporter().report(updateState);
            }

            saveUpdateState(JsonThings.gsonNE().toJson(updateState));
            // let's reload the cached version right away, so if any save
            // caused an issue, we'll see it now, not later.
            loadUpdateState(JsonThings.gsonNE());
        }
    }

    public void onUpdateState(ConsumerT<UpdateState, Exception> r) {

        UpdateState updateState = checkOutUpdateState();
        try {
            LibUtil.reThrow(()->r.accept(updateState));
            checkUpdateState(updateState);
        } finally {
            releaseUpdateState(updateState);
        }

    }

    public <T> T fromUpdateState(FunctionT<UpdateState, T, Exception> r) {

        UpdateState updateState = checkOutUpdateState();
        try {
            T result = LibUtil.reThrow(()->r.apply(updateState));
            checkUpdateState(updateState);
            return result;
        } finally {
            releaseUpdateState(updateState);
        }

    }

}
