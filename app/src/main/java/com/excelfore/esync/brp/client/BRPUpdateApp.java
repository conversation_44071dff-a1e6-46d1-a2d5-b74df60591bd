package com.excelfore.esync.brp.client;

import android.app.AlarmManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.PowerManager;
import android.os.SystemClock;
import android.util.Base64;
import android.util.Log;
import android.webkit.URLUtil;

import androidx.annotation.Nullable;
import androidx.security.crypto.EncryptedFile;
import androidx.security.crypto.MasterKey;

import com.android.volley.Cache;
import com.android.volley.ExecutorDelivery;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.RetryPolicy;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.BasicNetwork;
import com.android.volley.toolbox.DiskBasedCache;
import com.excelfore.esync.brp.BuildConfig;
import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.cfg.IDInfo;
import com.excelfore.esync.brp.cfg.IdentityNotAvailableException;
import com.excelfore.esync.brp.cfg.StaticConfigData;
import com.excelfore.esync.brp.db.ESyncDatabaseWrapper;
import com.excelfore.esync.brp.db.Settings;
import com.excelfore.esync.brp.db.UpdateState;
import com.excelfore.esync.brp.model.sync_js.WClientConfigElement;
import com.excelfore.esync.brp.model.sync_js.WCommonResponse;
import com.excelfore.esync.brp.model.sync_js.WDeviceInfoRequest;
import com.excelfore.esync.brp.net.AuthHeaderContract;
import com.excelfore.esync.brp.net.EmergencyRequestRunState;
import com.excelfore.esync.brp.net.Endpoints;
import com.excelfore.esync.brp.net.NetUtils;
import com.excelfore.esync.brp.net.OkHttpStack;
import com.excelfore.esync.brp.net.RequestRunState;
import com.excelfore.esync.brp.session.ClientRunState;
import com.excelfore.esync.brp.session.Scheduler;
import com.excelfore.esync.brp.session.SessionRequest;
import com.excelfore.esync.brp.test.TL;
import com.excelfore.esync.brp.util.Crypto;
import com.excelfore.esync.brp.util.DLog;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.MyUtil;
import com.excelfore.esync.brp.util.RunnableT;

import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.cert.CRL;
import java.security.cert.Certificate;
import java.security.cert.X509CRL;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.X509TrustManager;

import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

public class BRPUpdateApp extends Application implements Constants {

    public final List<RunnableT<Exception>> stateResetRunners = new ArrayList<>();

    private final Object creationLock = new Object();
    private final Object provisionLock = new Object();

    public final ClientRunState clientRunState = new ClientRunState(this);
    public final ConfigDataProvider configDataProvider = new ConfigDataProvider(this);

    private RequestQueue volleyQueue;
    private RequestQueue reportVolleyQueue;
    private ExecutorService executorService;

    private Settings settings;
    private Scheduler scheduler;
    private Crypto.SSLThings sslThings;
    private MasterKey masterKey;
    private Reporter reporter;
    private boolean ready;
    private PowerManager.WakeLock clientActivityWakeLock;
    private JsonThings jsonThings;
    static private String idHash;
    private boolean idHashSet;
    private ESyncDatabaseWrapper db;
    private boolean ueUpdating = false;
    private Throwable pendingKill;
    private String fazit = null;
    private String hwPartNumber = null;
    private String hwVersion = null;
    private String swPartNumber = null;
    private long runningId;
    private Set<Dispatcher> okDispatchers = new HashSet<>();
    private Set<ConnectionPool> okConnectionPools = new HashSet<>();
    private Set<okhttp3.Cache> okCaches = new HashSet<>();
    private MainActivity mainActivity;

    private final Set<ReadyFlags> init = EnumSet.allOf(ReadyFlags.class);


    @Override
    public void onCreate() {

        L.i(TAG, "Starting BRP eSync client "+ getVersion());

        TL.impl.appCreated();

        Crypto.loadProviders();

        super.onCreate();

        scheduler = new Scheduler(this);
        executorService = Executors.newFixedThreadPool(Constants.GENERAL_WORK_THREADS);
        // $TODO: this means that nothing will clean the dup logs
        // (except when the application is fake restarted)
        // If running under test, there's no need to submit the cleaning task.
        executorService.submit(()->{
            if (!LibUtil.isUnderTest()) {
                long time = System.currentTimeMillis();
                try {
                    DLog.clean();
                } catch (Exception e) {
                    L.w(TAG, "Cleaning DLog", e);
                }
                try {
                    Thread.sleep(DLog.CACHE_TIME.toMillis());
                } catch (InterruptedException ignored) {}
                long endTime = System.currentTimeMillis();
                L.d(TAG, "Cleaning the log messages finished and took " + (endTime - time) + "ms");
            }
            executorService.submit((Runnable)this);
        });

        // maintain running id value through reboot
        String strRunningId = "";
        SharedPreferences sp = getInternalState();
        runningId = sp.getLong(SP_U0_RUNNING_ID, -1);
        runningId = runningId + 1;
        if (!sp.edit().putLong(SP_U0_RUNNING_ID, runningId).commit()) {
            L.w(TAG, "Failed to save running id!");
        }
        strRunningId = "Running id:" + runningId;

        L.i(TAG, strRunningId);

        masterKey = new MasterKeyMaker(this).generate();

        jsonThings = new JsonThings(this);

        stateResetRunners.add(DLog::zeroOut);

        startWork();

    }

    public String getVersion() {

        StringBuilder sb = new StringBuilder();
        sb.append(BuildConfig.GIT_VERSION);
        if (!Objects.equals(BuildConfig.GIT_VERSION, BuildConfig.VERSION_NAME)) {
            sb.append("(release ").append(BuildConfig.VERSION_NAME).append(')');
        }
        sb.append('(').append(BuildConfig.VERSION_CODE).append(')').append('-').append(BuildConfig.FLAVOR);

        return sb.toString();

    }

    public static BRPUpdateApp getApp(Context ctx) {
        if (ctx instanceof BRPUpdateApp) {
            return (BRPUpdateApp) ctx;
        }
        return (BRPUpdateApp) ctx.getApplicationContext();
    }

    public Duration sessionInterval() {
        if (getCurrentOtaUrl() == null) {
            return Constants.POLLING_INTERVAL_NO_URL;
        }

        return getSettings().getSessionIntervalSec(
                configDataProvider.getStaticConfigData().sessionInterval);

    }

    public Settings getSettings() {
        if (settings == null) {
            settings = getDatabase().loadSettings();
        }
        return settings;
    }

    public String getCurrentOtaUrl() {

        var otaUrl = getSettings().getUrl();
        L.i(TAG, "No URL override found, using provisioned URL " + otaUrl);
        return otaUrl;

    }

    public synchronized ESyncDatabaseWrapper getDatabase() {
        if (db == null) {
            db = new ESyncDatabaseWrapper(this);
        }
        return db;
    }

    public synchronized byte[] getSharedEncryptionKey() {

        try (InputStream is = readFromEncrypted(MY_ENCRYPTED_SYMMETRIC_KEY_FILE)) {
            byte [] key = MyUtil.readFullySecure(is);
            if (key.length != 32) {
                throw new IllegalStateException("Key length expected 32, but got "+key.length);
            }
            return key;
        } catch (Exception e) {
            L.e(TAG, "Failed to read key file, regenerating", e);
        }

        return LibUtil.reThrow(() -> {
            byte[] keyBytes = new byte[32]; //32 Bytes = 256 Bits
            MyUtil.secureRandom.nextBytes(keyBytes);
            File keyFile = new File(getFilesDir(), MY_ENCRYPTED_SYMMETRIC_KEY_FILE);
            if (!keyFile.delete()) {
                L.e(TAG, "Failed to delete "+keyFile.getAbsolutePath());
            }
            try (OutputStream os = writeToEncrypted(MY_ENCRYPTED_SYMMETRIC_KEY_FILE)) {
                os.write(keyBytes);
                return keyBytes;
            }
        });
    }

    public FileInputStream readFromEncrypted(String file) {
        try {
            if (LibUtil.isUnderTest()) {
                // $TODO: It really should work without incursion
                return new FileInputStream(file);
            }
            return getEncrypted(file).openFileInput();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public EncryptedFile getEncrypted(String localFile) throws IOException, GeneralSecurityException {
        File file = new File(getFilesDir(), localFile);
        return new EncryptedFile.Builder(
                this,
                file,
                masterKey,
                EncryptedFile.FileEncryptionScheme.AES256_GCM_HKDF_4KB
        ).build();
    }

    public FileOutputStream writeToEncrypted(String file) {
        try {
            if (LibUtil.isUnderTest()) {
                // $TODO: It really should work without incursion
                return new FileOutputStream(file);
            }
            return getEncrypted(file).openFileOutput();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // note - only database should call this.
    public void updateSettings(Settings settings) {
        this.settings = settings;
    }

    public Reporter getReporter() {
        return getReporter(true);
    }

    public Reporter getReporter(boolean create) {

        synchronized (creationLock) {
            if (reporter == null) {
                if (!create) { return null; }
                reporter = new Reporter(this);
            }
            return reporter;
        }

    }

    public JsonThings getJsonThings() {
        return jsonThings;
    }

    public Map<String, String> makeExtraHeaders(AuthHeaderContract ahc) {

        Map<String, String> r = new HashMap<>();

        r.put("x-xl4-dmclient-version", getVersion());
        r.put("x-xl4-sota-feature", "no-oma");
        return r;
    }

    public void handleCommonResponse(JSONObject j) {

        WCommonResponse r = JsonThings.gsonNE().fromJson(j.toString(), WCommonResponse.class);

        Settings myCfg = getSettings();
        MyUtil.whenNotNull(r.getConfig(), cfg -> {
            myCfg.setServerSays(cfg);

            // Save server settings to DB
            getDatabase().saveSettings(myCfg);

            for (WClientConfigElement wce : cfg.getElements()) {
                if (wce.getPath().getStandard() == null) {
                    L.w(TAG, "Unrecognized setting " + wce.getPath().getCustom());
                }
            }
        });

        getDatabase().onUpdateState(us-> MyUtil.whenNotNull(us.getTarget(), t -> {
            if (r.getPendingAbort().getAborted().contains(t.getRecordId())) {
                t.setAborted(true);
            }
        }));

    }

    public String makeURL(String baseUrl, Endpoints endpoint) {

        Uri omaURI = Uri.parse(baseUrl);
        String omaPath = omaURI.getPath();
        String servicePath;

        switch (endpoint) {
            case CHECK:
                servicePath = CORE_SERVICE_PATH + "check";
                break;
            case CHECK_IN:
                servicePath = CORE_SERVICE_PATH + "check-in";
                break;
            case REPORTS:
                servicePath = CORE_SERVICE_PATH + "reports";
                break;
            default:
                throw new RuntimeException(endpoint.name());
        }

        return omaURI.buildUpon().encodedPath(Paths.get(omaPath, servicePath).normalize().toString()).build().toString();

    }

    public boolean isReady() {
        return ready;
    }

    public BRPUpdateApp setReady(ReadyFlags f) {

        synchronized (this) {

            L.i(TAG, "Initialization reported for "+f);

            init.remove(f);
            if (init.isEmpty()) {
                this.ready = true;
                getScheduler().scheduleNow(new SessionRequest("Application ready", false));
            } else {
                L.i(TAG, "Pending initialization "+init);
            }
            TL.impl.appReady(EnumSet.copyOf(init));

        }

        return this;
    }

    public PowerManager.WakeLock getClientPowerLock() {

        if (clientActivityWakeLock != null) {
            return clientActivityWakeLock;
        }

        PowerManager pm = (PowerManager) getSystemService(
                Context.POWER_SERVICE);
        return clientActivityWakeLock = pm.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                TAG + ":");

    }

    public ExecutorService getOurExecutorService() {
        return executorService;
    }

    public Scheduler getScheduler() {
        return scheduler;
    }

    public String getCurrentlyInstalledVersion(UpdateState us) {

        // BRP-9
        // If the .kss files are already present - this means we are at "updated"
        // version.

        if (haveNewKssFiles()) {
            // yes, we have KSS files, just say that we've updated to whatever
            // were were supposed to.
            var target = us.getTarget();
            if (target == null) { return null; }
            var tTarget = target.getTarget();
            if (tTarget == null) { return null; }
            String targetVersion = tTarget.getVersion();
            if (targetVersion == null) {
                L.i(TAG, "No target update version found, assuming old");
                return "v1";
            }
            L.i(TAG, "Assuming version as target: "+targetVersion);
            return targetVersion;
        }
        return "v1";


    }
    public String getCurrentlyInstalledVersion(UpdateHelper caller) {

        return caller.fromUpdateState(this::getCurrentlyInstalledVersion);

        /*
        // BRP-8 - return our own app's version
        if ("v1".equals(BuildConfig.FLAVOR)) {
            return "v1";
        }
        return BuildConfig.VERSION_CODE + "." + BuildConfig.FLAVOR;
         */

        /*
        if (LibUtil.isUnderTest()) {
            String version = getSharedPreferences(SP_FAKE_INSTALL, MODE_PRIVATE).getString("value", null);
            if (version != null) {
                return version;
            }
        }

        return Build.FINGERPRINT;
         */


    }

    private boolean haveNewKssFiles() {

        try {

            File kssDir = new File(KSS_LOCATION);
            if (!kssDir.exists()) {
                L.i(TAG, kssDir.getAbsolutePath()+" does not exist");
                return false;
            }
            if (!kssDir.isDirectory()) {
                L.i(TAG, kssDir.getAbsolutePath()+" is not a directory");
                return false;
            }
            var files = kssDir.listFiles(pathname -> {
                boolean ok = pathname.exists() && pathname.isFile();
                if (!ok) {
                    L.i(TAG, "Not considering "+pathname.getAbsolutePath()+" - not a regular file");
                } else {
                    L.i(TAG, "Considering "+pathname.getAbsolutePath()+" - looks OK");
                }
                return ok;
            });

            if (files != null && files.length >= 6) {
                Log.i(TAG, "Have 6 or more files, declaring as updated");
                return true;
            }

            if (files == null) {
                Log.i(TAG, "Null files, none? Declaring as not updated");
                return false;
            }

            Log.i(TAG, "Have less than 6 files, declaring as not updated: "+Arrays.asList(files));
            return false;

        } catch (Exception e) {
            L.e(TAG, "Error determining KSS files presence, assuming none", e);
            return false;
        }


    }

    public SharedPreferences getInternalState() {
        return getSharedPreferences(SP_INTERNAL_STATE, MODE_PRIVATE);
    }

    public void performSyncSettings(SessionWork task, Runnable onDone, boolean emergency) {
        SharedPreferences sp = getInternalState();

        Response.Listener<JSONObject> listener = j -> {

            try {
                handleCommonResponse(j);
            } finally {
                onDone.run();
            }

        };

        int method;

        method = Request.Method.POST;
        WDeviceInfoRequest req = new WDeviceInfoRequest();

        req.setFazit(fazit);
        req.setHwPartNumber(hwPartNumber);
        req.setHwVersion(hwVersion);
        req.setSwPartNumber(swPartNumber);

        RequestRunState rrs = emergency ? new EmergencyRequestRunState() : clientRunState.rrsCheckIn;

        NetUtils.scheduleJsonObjectRequest(task,
                method, makeCurrentURL(task, Endpoints.CHECK_IN), req, rrs,
                listener,
                (e)->{
                    NetUtils.logVolleyError(TAG, "Failed to get settings", e);
                    onDone.run();
                }, false);

    }

    public String makeCurrentURL(SessionWork w, Endpoints endpoint) {
        String url;
        if (w != null) {
            url = w.getCurrentSession().url;
        } else {
            url = getCurrentOtaUrl();
        }
        if (url == null) { return null; }
        return makeURL(url, endpoint);
    }

    public String getDownloadDir() {
        return Paths.get(getOtaDir(), "download").toString();
    }

    public String getExpandDir() {
        var f = new File(getCacheDir(), "expanded");
        f.mkdirs();
        return f.getAbsolutePath();
    }

    public String getOtaDir() {
        if (LibUtil.isUnderTest()) {
            String otaPath=Paths.get(getFilesDir().getAbsolutePath(), "ota_package").toString();
            File otaDir = new File(otaPath);
            if (!otaDir.exists()) {
                if (!otaDir.mkdirs()) {
                    L.e(TAG, "Failed to create dir: "+otaPath);
                }
            }
            return otaPath;
        }
        File f = new File(getDataDir(), "binaries");
        f.mkdirs();
        return f.getAbsolutePath();

    }


    public String getEsyncUpdatePayload() {
        return Paths.get(getOtaDir(), ESYNC_UPDATE).toString();
    }

    /**
     * Indicate whether the OTA client is actively executing an update.
     * The raised flag prevents the client from being killed by any uncaught exceptions
     * to prevent later inconsistency between the client and the update engine state.
     * Lowering the updating flag will cause client death if there was an
     * uncaught exception detected during the update.
     * @param how {@code true} to indicate active update is in progress, {@code false}
     * to indicate there is no active update (which should only happen if there was one that
     * has just finished).
     */
    public void setUpdating(boolean how) {
        ueUpdating = how;
        if (!how && pendingKill != null) {
            handleUncaughtException(pendingKill, true);
        }
    }

    private void handleUncaughtException(Throwable e, boolean delayed) {

        L.wtf(TAG, "That's embarrassing (delayed="+delayed+")", e);
        if (LibUtil.isUnderTest()) {
            TL.impl.embarrassment(this, e);
            return;
        }

        // Ignore unhandled exceptions when updating
        if (ueUpdating && !delayed) {
            L.w(TAG, "In the middle of an UE update, ignoring uncaught exception");
            pendingKill = e;
        } else {
            L.i(TAG, "Will restart application");
            // scheduleRestart();
            killSelf();
        }

    }

    private void scheduleRestart() {

        // https://www.thecodecity.com/2017/01/auto-restart-app-after-is-crashes.html
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_CLEAR_TASK
                | Intent.FLAG_ACTIVITY_NEW_TASK);

        PendingIntent pendingIntent;
        pendingIntent = PendingIntent.getBroadcast(this,
                Constants.IRC_APP_RESTART, intent, PendingIntent.FLAG_IMMUTABLE);

        // Restart your app after Constants.FATAL_ERROR_RESTART
        AlarmManager mgr = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.ELAPSED_REALTIME, SystemClock.elapsedRealtime() +
                Constants.FATAL_ERROR_RESTART.toMillis(), pendingIntent);
        // OK, this is brutal, and probably against some rules, but there are no better
        // suggestions than this.

    }

    public void killSelf() {
        if (!LibUtil.isUnderTest()) {
            L.w(TAG, "Self-destruct");
            if (mainActivity != null) {
                mainActivity.finishAffinity();
            }
            System.exit(2);
        }
    }

    public SSLSocketFactory getSSLSocketFactory() {
        return getSSLThings().sslSocketFactory;
    }

    public X509TrustManager getTrustManager() {
        return getSSLThings().sslTrustManager;
    }

    public SSLSocketFactory getSSLSocketFactoryPublic() {
        return getSSLThings().sslSocketFactoryPublic;
    }

    public X509TrustManager getTrustManagerPublic() {
        return getSSLThings().trustManagerPublic;
    }

    private Crypto.SSLThings getSSLThings() {

        synchronized (creationLock) {
            if (sslThings != null) {
                return sslThings;
            }
            sslThings = Crypto.makeSSLThings(this);
        }

        return sslThings;

    }

    public void registerOkResources(OkHttpClient client) {

        if (LibUtil.isUnderTest()) {
            Dispatcher d = client.dispatcher();
            ConnectionPool cp = client.connectionPool();
            okhttp3.Cache c = client.cache();
            if (d != null) { okDispatchers.add(d); }
            if (cp != null) { okConnectionPools.add(cp); }
            if (c != null) { okCaches.add(c); }
        }

    }
    public KeyStore getSignatureTrustStore() {
        return getSSLThings().signatureTrustStore;
    }

    @Nullable
    public CRL getCRL() {
        return getSSLThings().crl;
    }

    public File getConfigOverrideFile() {
        if (LibUtil.isUnderTest()) {
            return new File(getOtaDir(), Constants.OVERRIDE_CONFIG_FILE_NAME);
        } else {
            return Paths.get("/data", Constants.OVERRIDE_CONFIG_FILE_NAME).toFile();
        }
    }

    public long getRunningId() {
        return runningId;
    }

    public void scheduleWebServiceRequest(Request<?> httpRequest, boolean isReport) {

        try {
            String sBody = MyUtil.ifNotNull(httpRequest.getBody(), String::new, () -> "<no body>");
            L.d(TAG, "HTTP->" + NetUtils.volleyMethod(httpRequest.getMethod()) + "->" +
                    httpRequest.getUrl() + "->" + sBody);
        } catch (Exception e) {
            L.e(TAG, "Failed to log outgoing http message", e);
        }

        int timeoutMS = (int) getSettings().getWSTimeout().toMillis();

        // https://stackoverflow.com/questions/17094718
        httpRequest.setRetryPolicy(new RetryPolicy() {
            @Override
            public int getCurrentTimeout() {
                return timeoutMS;
            }

            @Override
            public int getCurrentRetryCount() {
                return 0;
            }

            @Override
            public void retry(VolleyError error) throws VolleyError {
                throw error;
            }
        });

        RequestQueue queue = isReport ? getReportVolleyQueue() : getVolleyQueue();
        queue.add(httpRequest);

    }

    private RequestQueue getVolleyQueue() {
        if (volleyQueue == null) {
            volleyQueue = createVolleyQueue();
        }
        return volleyQueue;
    }

    private RequestQueue getReportVolleyQueue() {
        if (reportVolleyQueue == null) {
            reportVolleyQueue = createVolleyQueue();
        }
        return reportVolleyQueue;
    }

    private RequestQueue createVolleyQueue() {

        // we have to initialize the request queue ourselves, since Volley.newRequestQueue()
        // doesn't allow us to set custom executor. We want custom executor because otherwise
        // main loop is used, and operations executed on the main loop are restricted, and
        // we'll have to escape into custom threads all the time, which is annoying.

        // https://stackoverflow.com/questions/39553999
        OkHttpStack netStack = new OkHttpStack(getSSLSocketFactory(), getTrustManager());
        File cacheDir = new File(getCacheDir(), Constants.VOLLEY_CACHE_DIR);
        BasicNetwork network = new BasicNetwork(netStack);
        Cache cache = new DiskBasedCache(cacheDir);
        ExecutorDelivery delivery = new ExecutorDelivery(getOurExecutorService());
        RequestQueue volleyQueue = new RequestQueue(cache, network, Constants.VOLLEY_THREAD_POOL_SIZE, delivery);
        volleyQueue.start();

        return volleyQueue;

    }

    /**
     * This is expected to only matter for tests, for cases when we attempt
     * to simulate a "device restart". We should clear any application fields
     * that are effective singletons.
     */
    private void clearCached() {
        settings = null;
    }


    public void startWork() {

        getOurExecutorService().execute(() -> {

            synchronized (provisionLock) {

                if (isReady()) {
                    return;
                }

                clearCached();
                provisionClient();
                setReady(ReadyFlags.PROVISION);

            }

        });

    }

    public void reportActivity(MainActivity a) {

        mainActivity = a;
        setReady(ReadyFlags.ACTIVITY);
        Thread.setDefaultUncaughtExceptionHandler((t, e) ->handleUncaughtException(e, false));

    }

    private void provisionClient() {

        try {

            ESyncDatabaseWrapper db = getDatabase();

            String serialNumber = null;
            String url = null;
            byte[] tlsCA = null;
            byte[] sigCA = null;
            byte[] crl = null;

            Settings settings = getSettings();

            try {

                IDInfo idInfo = getIdentity();

                boolean urlOk = validateIdentity(idInfo);

                L.i(TAG, "device id: " + (serialNumber = idInfo.serialNumber));
                L.i(TAG, "ota url: " + (url = urlOk ? idInfo.otaURL : null));
                L.i(TAG, "tls CA: " + idInfo.tlsCAStr());
                tlsCA = idInfo.tlsCA;
                L.i(TAG, "sig CA: " + idInfo.signatureCAStr());
                sigCA = idInfo.signatureCA;
                L.i(TAG, "crl: " + idInfo.crlStr());
                crl = idInfo.crl;

                idHashSet = true;

            } catch (IdentityNotAvailableException e) {
                L.e(TAG, "Factory identification was not successful", e);
            }

            // let's see if there are any overrides.
            StaticConfigData scd = configDataProvider.getStaticConfigData();
            if (scd.deviceId != null) {
                serialNumber = scd.deviceId;
                L.w(TAG, "Overriding device ID with "+serialNumber);
            }

            if (scd.url != null) {
                url = scd.url;
                L.w(TAG, "Overriding OTA URL with "+url);
            }

            if (scd.tlsCA != null) {
                L.w(TAG, "Overriding TLS CA with "+scd.tlsCA);
                tlsCA = Base64.decode(scd.tlsCA, Base64.DEFAULT);
            }

            if (scd.sigCA != null) {
                L.w(TAG, "Overriding Signature CA with "+scd.sigCA);
                sigCA = Base64.decode(scd.sigCA, Base64.DEFAULT);
            }

            if (scd.crl != null) {
                if (scd.crl.isEmpty()) {
                    L.w(TAG, "Overriding CRL with nothing");
                    crl = null;
                } else {
                    L.w(TAG, "Overriding CRL with "+scd.crl);
                    crl = Base64.decode(scd.crl, Base64.DEFAULT);
                }
            }

            boolean save = false;
            boolean rebuildCert = false;

            if (!Objects.equals(serialNumber, settings.getSerialNumber())) {
                settings.setSerialNumber(serialNumber);
                save = true;
                rebuildCert = true;
            }

            if (!Objects.equals(url, settings.getUrl())) {
                settings.setUrl(url);
                save = true;
            }

            if (!Arrays.equals(tlsCA, settings.getTlsCA())) {
                settings.setTlsCA(tlsCA);
                save = true;
                rebuildCert = true;
            }

            if (!Arrays.equals(sigCA, settings.getSigCA())) {
                settings.setSigCA(sigCA);
                save = true;
            }

            if (!Arrays.equals(crl, settings.getCrl())) {
                settings.setCrl(crl);
                save = true;
            }

            if (save) {
                L.i(TAG, "Settings updated");
                db.saveSettings(settings);
            } else {
                L.i(TAG, "Settings unchanged");
            }

            // force re-generate key/certificate when serialnumber or tlsCA was changed
            if (rebuildCert) {
                L.i(TAG, "Certificate info was changed, deleting current key/certificate");
                File keyFile = new File(getFilesDir(), Constants.MY_ENCRYPTED_KEY_FILE);
                File certFile = new File(getFilesDir(), Constants.MY_CERT_FILE);
                boolean deleteFail = !keyFile.delete();
                if (!certFile.delete()) { deleteFail = true; }
                if (deleteFail) {
                    L.e(TAG, "Failed to delete key/certificate file "+
                            keyFile.getAbsolutePath()+", "+certFile.getAbsolutePath());
                }
            }

        } catch (Exception e) {
            L.e(TAG, "Provisioning failed, OTA client will not be available", e);
            killSelf();
        }

    }

    private IDInfo getIdentity() throws IdentityNotAvailableException {

        try {

            byte [] cab;
            try (var ca = getAssets().open(Crypto.CA_PEM)) {
                cab = LibUtil.readFully(ca);
            }

            return new IDInfo("1KGEJ6605T0000005", "https://brp-esync-dev.esyncsdk.com:8443/snap/oma", false, cab, cab, null);

        } catch (IOException e) {
            throw new IdentityNotAvailableException("Error loading asset files", e);
        }


    }

    /**
     * Verifies whether the identity information is considered usable.
     * IMPLEMENTATION NOTE: we bork the use of the entire identity object if any of its pieces
     * (sans stipulated exceptions) doesn't make sense. This is done instead of ignoring only
     * the "bad" pieces, and moving on forward. Doing the latter is a bad idea because a problem
     * with a CA indicates that there may be a security issue, and proceeding further without
     * that piece would be reckless (this is especially true for CRL).
     * @param id identity data received from the FID
     * @return {@code true} if the URL in the identity service can be used successfully,
     * or {@code false} has to be assumed as "unset". This flag only is only meaningful if
     * {@link IDInfo#ignoreURL} is set to {@code true}; otherwise having an invalid OTA URL
     * will cause {@link IdentityNotAvailableException} exception to be thrown.
     * @throws IdentityNotAvailableException if the identity can't be used all together and
     * should not be considered.
     */
    public boolean validateIdentity(IDInfo id) throws IdentityNotAvailableException {

        if (id == null) {
            throw new IdentityNotAvailableException("ID object is null");
        }

        String sn = id.serialNumber;
        if (MyUtil.sTrim(sn) == null || "ase".equals(sn)) {
            throw new IdentityNotAvailableException("serial number is missing: "+sn);
        }

        // it may be OK for the URL to be missing.
        boolean urlOk = false;
        try {
            String url = id.otaURL;
            if (MyUtil.sTrim(url) == null || "ase".equals(url)) {
                throw new IdentityNotAvailableException("OTA URL is missing: " + url);
            }

            if (!URLUtil.isHttpsUrl(url)) {
                throw new IdentityNotAvailableException("OTA URL must be a secure (https) URL: " + url);
            }

            urlOk = true;

        } catch (IdentityNotAvailableException e) {
            if (!id.ignoreURL) {
                throw e;
            } else {
                L.e(TAG, "OTA URL from identity info is invalid, and is disabled: "+ e.getMessage());
            }
        }

        try {
            Certificate c = Crypto.loadCertificate(id.tlsCA);
            Crypto.validateIsCA(c);
        } catch (Exception e) {
            throw new IdentityNotAvailableException("Can not load/validate TLS CA: "+id.tlsCAStr(), e);
        }

        try {
            Certificate c = Crypto.loadCertificate(id.signatureCA);
            Crypto.validateIsCA(c);
        } catch (Exception e) {
            throw new IdentityNotAvailableException("Can not load/validate Signature CA"+id.signatureCAStr(), e);
        }

        byte [] crl = id.crl;
        if (crl != null) {
            try {
                CRL c = Crypto.loadCRL(crl);
                if (!(c instanceof X509CRL)) {
                    throw new IdentityNotAvailableException("Not X509 CRL : " + crl.getClass().getName());
                }
            } catch (Exception e) {
                throw new IdentityNotAvailableException("CRL is present by can not be validated: "+id.crlStr());
            }
        } else {
            L.w(TAG, "CRL is not present");
        }

        return urlOk;

    }

    public MainActivity getMainActivity() {
        return mainActivity;
    }

    public void handleConsent(Object ir, boolean b) {

        getOurExecutorService().submit(()->{

            var newSession = db.fromUpdateState((us)->{

                var p = us.getProgress();
                var t = us.getTarget();

                if (t == null) {
                    L.e(TAG, "Can not set consent - no target");
                    return false;
                }

                var id = t.getRecordId();
                if (!Objects.equals(ir, id)) {
                    L.e(TAG, "Ignoring consent request, current target is "+id+", consent is for "+ir);
                    return false;
                }

                var c = p.getConsent();
                if (c != null) {
                    L.e(TAG, "Can not change consent to "+b+" - already provided");
                    return false;
                }

                p.setConsent(b);
                Log.i(TAG, "Consent now provided for "+ir+" - "+b);
                return true;

            });

            if (newSession) {
                getScheduler().scheduleNow(new SessionRequest("Consent provided"));
            }

        });

    }
}
