package com.excelfore.esync.brp.client;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.cfg.StaticConfigData;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.MyUtil;

import java.io.File;
import java.io.Reader;

public class ConfigDataProvider {

    private StaticConfigData cda;
    private final BRPUpdateApp app;

    public ConfigDataProvider(BRPUpdateApp app) {
        this.app = app;
    }

    public StaticConfigData getStaticConfigData() {

        synchronized (ConfigDataProvider.class) {

            if (cda != null) {
                return cda;
            }

            StaticConfigData cda = null;
            File override = app.getConfigOverrideFile();
            try (Reader r = MyUtil.fileReader(override)) {
                cda = JsonThings.gsonNE().fromJson(r, StaticConfigData.class);
                L.w(Constants.TAG, "Read override config from "+override.getAbsolutePath()+
                        ", I sure hope this is an Excelfore testing run");
            } catch (Throwable t) {
                if (!override.exists()) {
                    L.i(Constants.TAG, "No CDA override in " + override.getAbsolutePath());
                } else {
                    L.e(Constants.TAG, "Could not load CDA from " + override.getAbsolutePath(), t);
                }
            }

            if (cda == null) { cda = new StaticConfigData(); }

            if (cda.networkRetryInterval == null) {
                cda.networkRetryInterval = Constants.NETWORK_RETRY_INTERVAL;
            }
            if (cda.networkRetryLimit == null) {
                cda.networkRetryLimit = Constants.NETWORK_RETRY_LIMIT;
            }
            if (cda.sessionInterval == null) {
                cda.sessionInterval = Constants.POLLING_INTERVAL;
            }
            if (cda.tenancy == null) {
                cda.tenancy = Constants.DEFAULT_TENANCY;
            }

            return this.cda = cda;

        }

    }

}
