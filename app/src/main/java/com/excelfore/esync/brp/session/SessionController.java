package com.excelfore.esync.brp.session;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.PowerManager;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.ClientResult;
import com.excelfore.esync.brp.client.ESyncClient;
import com.excelfore.esync.brp.client.UpdateHelper;
import com.excelfore.esync.brp.db.Settings;
import com.excelfore.esync.brp.model.sync_js.ClientMessage;
import com.excelfore.esync.brp.model.sync_js.ComponentState;
import com.excelfore.esync.brp.test.TL;
import com.excelfore.esync.brp.test.TestListener;
import com.excelfore.esync.brp.util.DescFuture;
import com.excelfore.esync.brp.util.L;
import com.google.common.util.concurrent.SettableFuture;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

public class SessionController extends UpdateHelper {

    public SessionController(@NonNull Context appContext) {
        super(BRPUpdateApp.getApp(appContext));
    }

    @SuppressWarnings("UnusedReturnValue")
    @SuppressLint("WakelockTimeout")
    public void startWork(SessionScheduleRequest ss) {

        L.i(Constants.TAG, "Considering session request "+ss);
        String ssId = ss.getId();

        if (!app.isReady()) {
            L.i(Constants.TAG, "Application not ready yet, will reschedule when ready");
            TL.impl.sessionRequestDismissed(ss, null);
            return;
        }

        app.getReporter().checkAlive();

        TL.impl.sessionRequestEntered(ss);

        ClientRunState crs = app.clientRunState;

        String otaUrl = app.getCurrentOtaUrl();
        CurrentSession session;

        synchronized (crs.eps.concurrencyLock) {
            CurrentSession cs = crs.eps.currentSession;
            if (cs != null) {
                SessionScheduleRequest runningOrigin = cs.origin;
                ss.postponedOrDismissed();
                if (ss.suppressIfRunning) {
                    L.d(Constants.TAG, "Update session not started, already running "+
                            runningOrigin.getId());
                    TL.impl.sessionRequestDismissed(ss, null);
                } else {
                    crs.eps.requestReschedule(ss);
                    L.d(Constants.TAG, "Update session not started, already running ("+
                            runningOrigin.getId() + "), scheduling for later: "+ssId);
                    TL.impl.rescheduleRequestAdded(ss);
                }
                return;
            } else {
                crs.eps.setCurrent(session = new CurrentSession(this, ss, otaUrl));
            }
        }

        TL.impl.sessionRequestStarted(ss);

        TL.impl.sessionProgress(ss, TestListener.SessionProgress.STARTED);

        if (otaUrl == null) {
            L.e(Constants.TAG, "No usable OTA URL found, cancelling session");
            TL.impl.sessionProgress(ss, TestListener.SessionProgress.NO_OTA_URL);
            handleSessionEnd(ss);
            return;
        }

        app.clientRunState.resetRRS();

        L.i(Constants.TAG, "Starting update session, version " +
                app.getVersion() + " - " + ssId);

        Settings settings = app.getSettings();
        L.i(Constants.TAG, "eSync server: "+otaUrl +
                ", device ID:" + settings.getSerialNumber());

        SettableFuture<ClientResult> result = SettableFuture.create();

        PowerManager.WakeLock wakeLock = app.getClientPowerLock();

        wakeLock.acquire();

        result.addListener(()->{

            wakeLock.release();

            try {
                ClientResult resultDone = result.get();
                if (resultDone.isSuccess()) {
                    L.i(Constants.TAG, "Update session "+ssId+" completed");
                } else {
                    L.i(Constants.TAG, "Update session "+ssId+" completed with an error");
                }
            } catch (Exception e) {
                L.e(Constants.TAG, "Update session "+ssId+" completed with an exception", e);
            }

            // Wait for all in-session tasks to be finished somehow.

            List<DescFuture<?>> waitFor = session.closeWork();

            L.i(Constants.TAG, "Waiting for all "+ssId+" session tasks to complete");

            waitFor.forEach(df -> {

                try {
                    df.future.get(Constants.TASKS_DONE_TIMEOUT.toMillis(), TimeUnit.MILLISECONDS);
                } catch (ExecutionException ignored) {
                } catch (Exception e) {
                    L.e(Constants.TAG, "Failed to ensure completion of "+df.name, e);
                }

            });

            L.i(Constants.TAG, "All session "+ssId+" tasks cleared");

            TL.impl.sessionProgress(ss, TestListener.SessionProgress.CLEARED_PARALLEL);

            handleSessionEnd(ss);

        }, app.getOurExecutorService());

        app.getOurExecutorService().execute(()->new ESyncClient(app, session).doWork(result));

    }

    /**
     * Handles end of a session started with the specified scheduling request.
     * The method will schedule a new session based on the pending reschedule request
     * if there is any, or schedule a new session based on the session interval/next request
     * execution eligibility.
     * @param endedRequest scheduling request that originated the session.
     */
    private void handleSessionEnd(SessionScheduleRequest endedRequest) {

        ClientRunState crs = app.clientRunState;

        TL.impl.sessionRequestCompleted(endedRequest);

        synchronized (crs.eps.concurrencyLock) {

            L.d(Constants.TAG, "End of session "+endedRequest+" - rescheduling");

            crs.eps.setCurrent(null);
            Scheduler scheduler = app.getScheduler();
            SessionScheduleRequest rr = crs.eps.getAndClearPendingRescheduleRequest();
            if (rr != null) {
                scheduler.schedule(rr);
                TL.impl.rescheduleRequestCleared(rr);
            } else {
                scheduler.scheduleAt(app.clientRunState.getNextSessionTimeMillis(),
                        true, new SessionRequest("next request available/session interval", false));
            }
            TL.impl.sessionProgress(endedRequest, TestListener.SessionProgress.ENDED);
        }

    }

    public void requestInstallationConsent(ESyncClient eSyncClient) {

        var ir = fromUpdateState(us->{
            var t = us.getTarget();
            if (t != null) {
                var rp = us.getReport();
                rp.setError(ClientMessage.F.BRP_CONSENT_REQUESTED.make());
                rp.setComponentState(ComponentState.READY_TO_UPDATE);
                return t.getRecordId();
            }
            return null;
        });

        if (ir != null) {
            app.getMainActivity().showConsent(ir);
        }

    }

    /**
     * Part of {@link ClientRunState} that should only be accessible by {@link SessionController} class.
     */
    public static class ProtectedState {

        /**
         * Protects {@link #currentSession} and {@link #reschedule}, to control
         * that no two sessions can run in parallel.
         */
        final Object concurrencyLock = new Object();
        private CurrentSession currentSession;

        /**
         * Set to indicate that the client must restart right after it's done.
         * This is set by an attempt to execute a client when one is already running.
         * Protected by {@link #concurrencyLock}
         */
        private SessionScheduleRequest reschedule;

        /**
         * Sets state for the currently running session.
         * The caller must hold a lock on {@link #concurrencyLock} while calling this method.
         * An attempt to set current session when it's already set will result in an exception.
         * @param c current session state, {@code null} to indicate
         * that the session stopped, and is no longer running.
         *
         */
        public void setCurrent(CurrentSession c) {
            // this tests that we own the monitor
            concurrencyLock.notify();
            if (currentSession != null && c != null) {
                throw new IllegalStateException("Session is already running!");
            }
            currentSession = c;
        }

        public CurrentSession getCurrent() {
            return currentSession;
        }

        /**
         * Requests that the session execution is rescheduled due to concurrent execution attempt.
         * The caller must hold a lock on {@link #concurrencyLock} while calling this method.
         * @param what previous schedule.
         */
        public void requestReschedule(SessionScheduleRequest what) {
            // this tests that we own the monitor
            concurrencyLock.notify();
            reschedule = what;
        }

        /**
         * Returns current reschedule request (can be {@code null}), and removes it from
         * the run state. The implementation must either schedule the session based on
         * the returned reschedule request, or use normal scheduling if it's {@code null}.
         * @return reschedule request, or {@code null} if there isn't any.
         */
        public SessionScheduleRequest getAndClearPendingRescheduleRequest() {

            // this tests that we own the monitor
            concurrencyLock.notify();

            if (reschedule != null) {
                SessionScheduleRequest rq = reschedule.reschedule();
                // Actually remove the reschedule request,
                // as the method documentation says it should.
                reschedule = null;
                return rq;
            }

            return null;

        }

    }

}