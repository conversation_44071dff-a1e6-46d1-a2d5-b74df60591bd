package com.excelfore.esync.brp.dl;


import com.excelfore.esync.brp.model.sync_js.WDownload;
import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.MyUtil;
import com.excelfore.esync.brp.util.RunnableT;

import java.io.FileOutputStream;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;

import javax.crypto.Cipher;

public class CipherDecrypts extends Decrypts<RandomAccessFile> {

    final Cipher cipher;

    public CipherDecrypts(WDownload dlInfo, Downloader dl, ConsumerT<RunnableT<Exception>, Exception> writeOp, CopyWithMax hash) {
        super(dlInfo, dl, writeOp, hash);
        this.cipher = MyUtil.getCipher(dlInfo.getKey().getValue(dl.app), dlInfo.getMethod());
    }

    @Override
    protected void trim() throws Exception {
        // only in java this must that complicated...
        try (FileOutputStream fos = new FileOutputStream(downloader.updateFile, true);
             FileChannel outChan = fos.getChannel()) {
            outChan.truncate(maxSize);
        }
    }

    @Override
    protected int getBlockSize() {
        return 1024 * cipher.getBlockSize();
    }

    @Override
    protected RandomAccessFile makeWriter() throws Exception {
        return new RandomAccessFile(downloader.updateFile, "rw");
    }

    @Override
    protected byte[] decrypt(byte[] input, int len) {
        return cipher.update(input, 0, len);
    }

    @Override
    protected byte[] finishDecrypt() throws Exception {
        return cipher.doFinal();
    }
}
