package com.excelfore.esync.brp;

import java.time.Duration;

public interface Constants {

    String TAG = "eSync";

    Duration DEFAULT_DOWNLOAD_CONNECT_TIMEOUT = Duration.ofSeconds(30);
    Duration DEFAULT_DOWNLOAD_READ_TIMEOUT = Duration.ofSeconds(30);
    Duration DEFAULT_DOWNLOAD_SKIP_TIMEOUT = Duration.ofSeconds(30);

    String WORK_REPORTER = "esync.reporter";

    String MY_CERT_FILE = "client.cert";
    String MY_ENCRYPTED_KEY_FILE = "client.key";
    String MY_ENCRYPTED_SYMMETRIC_KEY_FILE = "shared.key";

    int MAX_REPORT_QUEUE_SIZE = 5000;

    String CORE_SERVICE_PATH = "../dmclient/js/v1/sync/";

    String SP_REPORT_SEQUENCE = "report.sequence";
    String SP_FAKE_INSTALL = "fake.version";
    String SP_INTERNAL_STATE = "esync.internal";
    String SP_LOGGER_STATE = "esync.lm-state";
    String SP_MBB_ERR_COUNT_COUNT = "mbb.error.count";

    String ESYNC_UPDATE = "esync-update.zip";

    // Running id of app (run by user 0)
    String SP_U0_RUNNING_ID = "u0.run.id";

    // Session id (run by user 0)
    String SP_U0_SESSION_ID = "u0.session.id";

    String PREF_REPORT_SEQUENCE = "seq";

    String REPORT_QUEUE_FILE = "reportQueue";

    // because we can't query Volley for it...
    String VOLLEY_CACHE_DIR = "volley";
    // that's what Volley uses by default
    int VOLLEY_THREAD_POOL_SIZE = 1;

    int GENERAL_WORK_THREADS = 8;

    Duration RETRY_DOWNLOAD = Duration.ofSeconds(30);

    Duration NETWORK_RETRY_INTERVAL = Duration.ofMinutes(5);
    long NETWORK_RETRY_LIMIT = 5;

    /**
     * This file, if present, contains configuration overrides for some built-in configuration
     * values. The reason for changing those is to have Excelfore QA use different configuration
     * values, otherwise testing may take too long of a time. This file must never be present
     * in production.
     */
    String OVERRIDE_CONFIG_FILE_NAME = "excelfore_config.json";

    Duration FATAL_ERROR_RESTART = Duration.ofSeconds(5);

    String DEFAULT_TENANCY = "default";

    // Maximum time for waiting tasks done
    Duration TASKS_DONE_TIMEOUT = Duration.ofSeconds(30);

    Duration POLLING_INTERVAL = Duration.ofMinutes(5);
    Duration POLLING_INTERVAL_NO_URL = Duration.ofMinutes(5);

    /**
     * The amount of time the reporter should sleep if the attempt to connect
     * to the backend failed because no token could be produced due to no current
     * secondary users. The value can be relatively large, as the reporter will be
     * poked to wake up when secondary users connect (rather, when user switch events
     * are posted).
     */
    Duration REPORTER_DELAY_ON_BLOCKED = Duration.ofHours(1);

    Duration WS_TIMEOUT = Duration.ofSeconds(30);

    int MAX_VERIFICATION_ERRORS = 1;
    int RETRY_BIND_SYSTEM_APP_SEC = 2;

    String NODE_NAME = "BRP-POC-FULL";

    int IRC_APP_RESTART = 4800;

    long BYTES_IN_KIBIBYTE = 1024;
    long BYTES_IN_MEBIBYTE = 1024 * BYTES_IN_KIBIBYTE;

    String CA_PEM = "ssl/ca.pem";
    String INT_PEM = "ssl/int.pem";
    String INT_KEY_PEM = "ssl/int_key.pem";
    String SHA_BIN = "ssl/sha.bin";

    String SYSTEM_CA_PATH = "/system/etc/security/cacerts";

    String DEVICE_AUTH_SOTA = "sota";
    String OID_DEVICE_AUTH = "1.3.6.1.4.1.45473.1.1";
    String OID_SOTA_DEVICE_ID = "1.3.6.1.4.1.45473.1.10";
    String OID_SOTA_TENANCY = "1.3.6.1.4.1.45473.1.5";

    String BEGIN_CERT = "-----BEGIN CERTIFICATE-----";
    String END_CERT = "-----END CERTIFICATE-----";
    String BEGIN_X509_KEY = "-----BEGIN PRIVATE KEY-----";
    String END_X509_KEY = "-----END PRIVATE KEY-----";

    String KSS_LOCATION = "/data/swupdate/kss_bins";
    // String PL_FILES_LOCATION = "/data/user/10/com.example.android.systemupdatersample/files";
    // String PL_CONFIGS_LOCATION = "/data/user/10/com.example.android.systemupdatersample/files/configs";
    String PL_FILES_LOCATION = "/data/swupdate/ota";
    String PL_CONFIGS_LOCATION = "/data/swupdate/ota";

}
