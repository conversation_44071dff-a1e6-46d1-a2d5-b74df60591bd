package com.excelfore.esync.brp.net;

import com.android.volley.Header;
import com.android.volley.NetworkResponse;
import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.excelfore.esync.brp.Constants;
import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.SessionWork;
import com.excelfore.esync.brp.util.CFormat;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.MutableBoolean;
import com.excelfore.esync.brp.util.MyUtil;

import org.json.JSONObject;

import java.util.Map;
import java.util.function.Function;

public class NetUtils {

    public static void scheduleJsonObjectRequest(SessionWork up,
            int method, String url, Object body, RequestRunState rrs,
            Response.Listener<JSONObject> listener,
            Response.ErrorListener errorListener, boolean isReport) {

        // $TODO this is crooked. The request/response types are both
        // forced to JSONObject instances, which shouldn't be, they should
        // be separate.

        scheduleRequest(new RequestInfo<>(up.app, up, method, url, body, rrs, listener,
                errorListener, NetUtils::makeJsonObjectRequest, isReport));
    }

    public static <T> void scheduleRequest(BRPUpdateApp app,
                                           int method, String url, Object body, RequestRunState rrs,
                                           Response.Listener<T> listener,
                                           Response.ErrorListener errorListener, Function<RequestInfo<T>,
            Request<T>> requestMaker, boolean isReport) {

        scheduleRequest(new RequestInfo<>(app, null, method, url, body, rrs, listener,
                errorListener, requestMaker, isReport));
    }

    public static Request<JSONObject> makeJsonObjectRequest(RequestInfo<JSONObject> ri) {

        JSONObject jBody = MyUtil.ifNotNull(ri.body, b->new JSONObject(JsonThings.gsonNE().toJson(ri.body)));

        return new JsonObjectRequest(ri.method, ri.url, jBody, ri.listener, ri.errorListener) {
            @Override
            protected Response<JSONObject> parseNetworkResponse(NetworkResponse response) {
                Response<JSONObject> result;
                if (!response.notModified) {
                    result = super.parseNetworkResponse(response);
                    logOKNetworkResponse(response, result.result);
                } else {
                    result = Response.error(new VolleyError(response));
                }
                return result;
            }
            @Override
            public Map<String, String> getHeaders() {
                return ri.app.makeExtraHeaders(ri);
            }

        };

    }

    public static <T> void scheduleRequest(RequestInfo<T> ri) {
        ri.rrs.ackTried();
        RequestDelayInfo rdi = ri.rrs.nextTimeMsRelative();

        // Request is always allowed when last error is "MBB Error No Secondary User"
        if (rdi.delayMs > 0) {

            Runnable doIt = ()->ri.errorListener.onErrorResponse(new MeteredCallVolleyError(rdi, ri));
            if (ri.task != null) {
                ri.task.submitWork("volley error listener", doIt);
            } else {
                ri.app.getOurExecutorService().execute(doIt);
            }
            return;

        }

        L.d(Constants.TAG, "Rate limit OK: "+rdi.explanation);
        Request<T> rq = ri.requestMaker.apply(ri);
        rq.setShouldCache(false);
        ri.app.scheduleWebServiceRequest(rq, ri.isReport);
    }

    public static void logOKNetworkResponse(NetworkResponse nr, Object data) {
        L.d(Constants.TAG, "HTTP<-"+networkPiece(nr) + sanitize(data));
        if (nr.allHeaders != null) {
            for (Header h : nr.allHeaders) {
                if ("x-xl4-snapstore-version".equals(h.getName())) {
                    L.d(Constants.TAG, "Reported server version: " + h.getValue());
                }
            }
        }
    }

    private static String sanitize(Object d) {

        if (d == null) { return "<null>"; }

        String s = d.toString();

        StringBuilder out = new StringBuilder();

        while (true) {

            int i = s.indexOf("\"key\"");
            if (i < 0) {
                out.append(s);
                break;
            }

            // this is crude, but should work, and at most may wipe out more stuff
            // than what's needed, but that's just sad, not critical.

            out.append(s.substring(0, i));
            out.append("\"key\"");
            // skip "key"
            i += 5;
            // search for first double quote
            int l = s.length();
            for (; i<l; i++) {
                char c = s.charAt(i);
                if (c == '"') { break; }
                out.append(c);
            }
            i++;
            // search for second '"'
            boolean escaped = false;
            for (; i<l; i++) {
                if (escaped) { escaped = false; continue; }
                char c = s.charAt(i);
                if (c == '\\') {
                    escaped = true;
                    continue;
                }
                if (c == '"') {
                    break;
                }
            }
            i++;

            out.append("\"--censored--\"");

            if (i >= l) { break; }

            s = s.substring(i);

        }

        return out.toString();

    }

    public static String gTime(long t) {
        return new CFormat("%,d").format(t);
    }

    private static String networkPiece(NetworkResponse nr) {
        return nr.statusCode + "<-" + gTime(nr.networkTimeMs) + "ms<-";
    }

    private static RequestFinishState considerRequestForDelayedRetry(NetworkResponse nr) {

        if (nr == null) {
            // if request finished without even producing a status,
            // this means it was an I/O error (including timeout), and
            // we should retry later.
            return RequestFinishState.ERROR_COUNT;
        }

        int code = nr.statusCode;

        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes

        // consider request for retry only if there is an indication
        // that it failed in such a way that it will benefit from recovery.
        // that means - the response code must indicate so directly or indirectly.

        if (code == 429 /*Too Many Requests*/ || code >= 500) {
            return RequestFinishState.ERROR_COUNT;
        }

        return RequestFinishState.FULL_COUNT;

    }

    public static Response.ErrorListener makeVolleyErrorListener(RequestInfo<?> ri,
            Response.ErrorListener original) {

        return e -> {

            MutableBoolean willRetry = new MutableBoolean();

            if (e instanceof MeteredCallVolleyError) {

                L.w(Constants.TAG, e.toString());

            } else {

                // $TODO: we handle "not modified" specially, mostly to make it
                // look like less of an error in the logs (it's no longer applied
                // to delayed retry logic). But some endpoints may also have
                // certain 4xx codes to be considered "normal" responses. This should
                // be fixed by having the response code handling logic be provided
                // by the caller.

                boolean isNotModified = e.networkResponse != null &&
                        e.networkResponse.notModified;

                StringBuilder sb = new StringBuilder("HTTP ");
                if (isNotModified) {
                    sb.append("NOT-MODIFIED");
                } else {
                    sb.append("ERR");
                }
                sb.append("<-");
                MyUtil.whenNotNull(e.networkResponse, n-> sb.append(networkPiece(n)));
                sb.append(gTime(e.getNetworkTimeMs()));
                sb.append("ms");
                if (isNotModified) {
                    L.i(Constants.TAG, sb.toString());
                } else {
                    L.e(Constants.TAG, sb.toString(), e);
                }

                // handle request state, but only if we are not going to retry the request
                // right after
                if (!willRetry.get()) {

                    RequestFinishState rfs = makeRequestFinishState(ri, e);

                    ri.rrs.ackFinished(rfs);

                }

            }

            if (willRetry.get()) {
                scheduleRequest(ri);
            } else {
                if (original != null) {
                    original.onErrorResponse(e);
                }
            }
        };
    }

    private static RequestFinishState makeRequestFinishState(RequestInfo<?> ri, VolleyError e) {
        RequestFinishState rfs;

        // Handle request rate for MBB Error case.
        if (ri.isMbbNoSource()) {
            rfs = RequestFinishState.BLOCKED;
        } else {
            // Check response code to see if we should follow retry rate
            // or consider the request concluded and apply normal request rate.
            rfs = considerRequestForDelayedRetry(e.networkResponse);
        }
        return rfs;
    }

    public static String volleyMethod(int method) {
        switch (method) {
            case Request.Method.GET:
                return "GET";
            case Request.Method.POST:
                return "POST";
            default:
                return "(Unknown:" + method + ")";
        }
    }

    public static void logVolleyError(String tag, String msg, VolleyError e) {

        if (e instanceof MeteredCallVolleyError) {
            L.w(tag, msg + ": call rate limit exceeded");
        } else {
            L.e(tag, msg, e);
        }

    }

}
