package com.excelfore.esync.brp.test;

import android.content.Intent;

import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.ReadyFlags;
import com.excelfore.esync.brp.session.SessionScheduleRequest;

import java.util.Set;


public interface TestListener {

    default void sessionProgress(SessionScheduleRequest ssr, SessionProgress p) {}
    default void sessionRequestScheduled(SessionScheduleRequest r) {}
    default void sessionRequestDismissed(SessionScheduleRequest r, SessionScheduleRequest replacement) {}
    default void sessionRequestEntered(SessionScheduleRequest r) {}
    default void sessionRequestStarted(SessionScheduleRequest r) {}
    default void sessionRequestCompleted(SessionScheduleRequest r) {}
    default void rescheduleRequestAdded(SessionScheduleRequest r) {}
    default void rescheduleRequestCleared(SessionScheduleRequest r) {}
    default void appReady(Set<ReadyFlags> set) {}

    default void appCreated() {}

    default void embarrassment(BRPUpdateApp app, Throwable e) {}

    default boolean isTestClosing() {return false;}

    default void broadcastReceived(Intent intent) {}

    default void appInstantiated(BRPUpdateApp app) {}

    enum SessionProgress {

        /**
         * The session has started,
         */
        STARTED,
        /**
         * the session found no OTA URL and will end right away
         */
        NO_OTA_URL,
        /**
         * the session has confirmed that all parallel tasks started by the session,
         * including notification delivery, has been confirmed cleared (or a timeout has been
         * reached).
         */
        CLEARED_PARALLEL,
        /**
         * The session has ended.
         */
        ENDED;

    }

}
