package com.excelfore.esync.brp.model.sync_js;

public class WDeviceInfoRequest {

    private WDeviceErrors errors;
    private String fazit;
    private String fingerprint;
    private String hwPartNumber;
    private String hwVersion;
    private String swPartNumber;

    private WDeviceLogState logState;

    private WLogDirectory logs;

    public WDeviceErrors getErrors() {
        return errors;
    }

    public WDeviceInfoRequest setErrors(WDeviceErrors errors) {
        this.errors = errors;
        return this;
    }

    public WDeviceInfoRequest setLogState(WDeviceLogState logState) {
        this.logState = logState;
        return this;
    }

    public WDeviceInfoRequest setLogs(WLogDirectory logs) {
        this.logs = logs;
        return this;
    }


    public String getFazit() {
        return fazit;
    }

    public WDeviceInfoRequest setFazit(String fazit) {
        this.fazit = fazit;
        return this;
    }

    public String getFingerprint() {
        return fingerprint;
    }

    public WDeviceInfoRequest setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
        return this;
    }

    public String getHwPartNumber() {
        return hwPartNumber;
    }

    public WDeviceInfoRequest setHwPartNumber(String hwPartNumber) {
        this.hwPartNumber = hwPartNumber;
        return this;
    }

    public String getHwVersion() {
        return hwVersion;
    }

    public WDeviceInfoRequest setHwVersion(String hwVersion) {
        this.hwVersion = hwVersion;
        return this;
    }

    public String getSwPartNumber() {
        return swPartNumber;
    }

    public WDeviceInfoRequest setSwPartNumber(String swPartNumber) {
        this.swPartNumber = swPartNumber;
        return this;
    }
}
