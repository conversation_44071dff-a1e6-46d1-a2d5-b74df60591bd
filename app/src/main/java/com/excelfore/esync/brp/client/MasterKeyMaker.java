package com.excelfore.esync.brp.client;

import android.content.Context;
import android.security.keystore.KeyGenParameterSpec;
import android.security.keystore.KeyProperties;

import androidx.security.crypto.MasterKey;


public class Master<PERSON>eyMaker {

    private final Context ctx;

    public MasterKeyMaker(Context ctx) {
        this.ctx = ctx;
    }

    public MasterKey generate() {

        try {
            // https://stackoverflow.com/questions/62498977
            KeyGenParameterSpec spec = new KeyGenParameterSpec.Builder(
                    MasterKey.DEFAULT_MASTER_KEY_ALIAS,
                    KeyProperties.PURPOSE_ENCRYPT | KeyProperties.PURPOSE_DECRYPT)
                    .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                    .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                    .setKeySize(MasterKey.DEFAULT_AES_GCM_MASTER_KEY_SIZE)
                    .build();

            return new MasterKey.Builder(ctx)
                    .setKeyGenParameterSpec(spec)
                    .build();

        } catch (Exception e) {
            // we really can't do much if this fails.
            throw new RuntimeException(e);
        }

    }


}
