package com.excelfore.esync.brp.db;


import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.FunctionT;

/**
 * Database execution request.
 */
public class OnDB<T> {

    public final String name;
    public final FunctionT<ESyncDatabase, T, Exception> op;
    public final boolean write;

    public OnDB(String name, boolean writes, FunctionT<ESyncDatabase, T, Exception> op) {
        this.name = name;
        this.op = op;
        write = writes;
    }

    public OnDB(String name, FunctionT<ESyncDatabase, T, Exception> op) {
        this(name, false, op);
    }

    public static OnDB<?> writeOnly(String name, ConsumerT<ESyncDatabase, Exception> op) {
        return new OnDB<Void>(name, true, db->{
            op.accept(db);
            return null;
        });
    }

}
