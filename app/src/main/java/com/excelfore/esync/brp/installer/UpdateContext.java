package com.excelfore.esync.brp.installer;

import static com.excelfore.esync.brp.Constants.TAG;

import com.excelfore.esync.brp.client.MainActivity;
import com.excelfore.esync.brp.client.SessionWork;
import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.MeasuringInputStream;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.Consumer;
import java.util.zip.ZipInputStream;

public class UpdateContext {

    public final static String ID = UpdateContext.class+".ID";

    private final ZipInputStream zis;
    private final MeasuringInputStream mis;
    private final FileInputStream fis;
    private final MainActivity activity;
    public final String id;

    private Consumer<BigDecimal> reportProgressTo;

    public UpdateContext(SessionWork caller, File downloadFile) throws Exception {

        boolean ok = false;

        try {

            fis = new FileInputStream(downloadFile);
            var fullSize = new BigDecimal(downloadFile.length());
            mis = new MeasuringInputStream(fis, false) {
                @Override
                protected void measure(byte[] b, int off, int len) {
                    super.measure(b, off, len);
                    var rp = reportProgressTo;
                    if (rp != null) {
                        rp.accept(BigDecimal.valueOf(size).divide(fullSize, 3, RoundingMode.HALF_UP));
                    }
                }
            };
            zis = new ZipInputStream(mis);

            var cs = caller.getCurrentSession();
            id = cs.app.getRunningId() + "." + cs.origin.serial;
            activity = cs.app.getMainActivity();

            L.d(TAG, "Update context created, id="+id);

            ok = true;

        } finally {
            if (!ok) { close(); }
        }

        activity.setUCtx(this);

    }



    /** @noinspection Convert2MethodRef*/ // because we want NPE to be thrown inside
    public void close() {

        reportProgressTo = null;

        LibUtil.noThrow(()->zis.close());
        LibUtil.noThrow(()->mis.close());
        LibUtil.noThrow(()->fis.close());

        if (activity != null) {
            activity.removeUCtx(this);
        }

    }

    public ZipInputStream getZis() {
        return zis;
    }

    public UpdateContext setReportProgressTo(Consumer<BigDecimal> reportProgressTo) {
        this.reportProgressTo = reportProgressTo;
        return this;
    }
}
