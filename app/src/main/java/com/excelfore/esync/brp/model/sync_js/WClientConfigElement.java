package com.excelfore.esync.brp.model.sync_js;

public class WClientConfigElement {

    private WConfigElementPath path;
    private WConfigElementType type;
    private Object value;

    public WClientConfigElement() {}

    public WConfigElementPath getPath() {
        return path;
    }

    public WClientConfigElement setPath(WConfigElementPath path) {
        this.path = path;
        return this;
    }

    public WConfigElementType getType() {
        return type;
    }

    public WClientConfigElement setType(WConfigElementType type) {
        this.type = type;
        return this;
    }

    public Object getValue() {
        return value;
    }

    public WClientConfigElement setValue(Object value) {

        if (value instanceof Number) {
            value = ((Number) value).longValue();
        }

        this.value = value;
        return this;
    }


}
