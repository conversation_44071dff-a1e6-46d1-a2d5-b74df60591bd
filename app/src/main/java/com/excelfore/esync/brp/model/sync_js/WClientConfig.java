package com.excelfore.esync.brp.model.sync_js;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import org.jetbrains.annotations.TestOnly;

import java.util.ArrayList;
import java.util.List;

public class WClientConfig {

    private List<WClientConfigElement> elements;

    public WClientConfig() {}

    @TestOnly
    public WClientConfig(List<WClientConfigElement> one) {
        elements = one;
    }

    @NonNull
    public List<WClientConfigElement> getElements() {
        if (elements == null) {
            elements = new ArrayList<>();
        }
        return elements;
    }

    @Nullable
    public WClientConfigElement find(WConfigElementPathEnum e) {
        return find(e.getValue());
    }

    @Nullable
    public WClientConfigElement find(@NonNull String s) {

        for (WClientConfigElement e : getElements()) {
            if (s.equals(e.getPath().getPath())) {
                return e;
            }
        }
        return null;

    }


}
