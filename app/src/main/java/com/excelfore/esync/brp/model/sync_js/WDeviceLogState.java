package com.excelfore.esync.brp.model.sync_js;

public class WDeviceLogState {
    // Current device log request state
    private WLogRequestState request;
    // The log request sequence number that the device is operating upon
    private Long sequence;

    private String loggerError;

    public WDeviceLogState setRequest(WLogRequestState state) {
        this.request = state;
        return this;
    }

    public WDeviceLogState setSequence(Long sequence) {
        this.sequence = sequence;
        return this;
    }

    public WDeviceLogState setLoggerError(String loggerError) {
        this.loggerError = loggerError;
        return this;
    }
}
