package com.excelfore.esync.brp.model.sync_js;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.annotations.JsonAdapter;

import java.lang.reflect.Type;

@JsonAdapter(WConfigElementPath.SD.class)
public class WConfigElementPath {

    private WConfigElementPathEnum standard;
    private String custom;

    public WConfigElementPath() {}

    public WConfigElementPathEnum getStandard() {
        return standard;
    }

    public WConfigElementPath setStandard(WConfigElementPathEnum standard) {
        this.standard = standard;
        return this;
    }

    public String getCustom() {
        return custom;
    }

    public WConfigElementPath setCustom(String custom) {
        this.custom = custom;
        return this;
    }

    public String getPath() {

        if (standard != null) { return standard.getValue(); }
        return custom;

    }

    public static class SD implements JsonSerializer<WConfigElementPath>, JsonDeserializer<WConfigElementPath> {

        @Override
        public WConfigElementPath deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                throws JsonParseException {
            WConfigElementPath path = new WConfigElementPath();
            String in = json.getAsString();
            WConfigElementPathEnum stdValue = WConfigElementPathEnum.resolver.fromString(in);
            if (stdValue != null) {
                path.setStandard(stdValue);
            } else {
                path.setCustom(in);
            }

            return path;
        }

        @Override
        public JsonElement serialize(WConfigElementPath src, Type typeOfSrc, JsonSerializationContext context) {
            if (src.standard != null) {
                return new JsonPrimitive(src.standard.getValue());
            } else if (src.custom != null) {
                return new JsonPrimitive(src.custom);
            } else {
                throw new IllegalStateException("Can't have both standard and custom paths set");
            }
        }
    }

}
