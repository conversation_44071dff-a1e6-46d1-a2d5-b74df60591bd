package com.excelfore.esync.brp.model.sync_js;

import androidx.annotation.NonNull;

import com.excelfore.esync.brp.db.ChangeableObject;
import com.excelfore.esync.brp.util.JsonThings;
import com.excelfore.esync.brp.util.LibUtil;
import com.excelfore.esync.brp.util.RFC3339;


/**
 * This represents the report object sent to eSync server
 */
public class WInstallationRecord extends ChangeableObject implements Cloneable {

    private String id;
    private String error;
    private ComponentState componentState;
    private OMAUpdateState updateState;
    private Integer downloadPercent;
    private UserState userStatus;
    private RFC3339 userStatusExpiry;
    private Integer failureCount;
    private String reportedVersion;
    private String reportedHash;
    private boolean terminal;
    private Boolean aborted;

    // report queue only, not subject to changeable logic
    private String sequence;
    private long stateSequence;
    private RFC3339 reported;
    private String url; // URL to use for reporting, as current OTA URL can already be different

    public WInstallationRecord setComponentState(@NonNull ComponentState componentState) {
        if (logDiff("componentState", this.componentState, componentState)) {
            this.componentState = componentState;
        }
        return this;
    }

    public WInstallationRecord setUpdateState(OMAUpdateState updateState) {
        if (logDiff("updateState", this.updateState, updateState)) {
            this.updateState = updateState;
        }
        return this;
    }

    public WInstallationRecord setDownloadPercent(Integer downloadPercent) {
        if (logDiff("downloadPercent", this.downloadPercent, downloadPercent)) {
            this.downloadPercent = downloadPercent;
        }
        return this;
    }

    public WInstallationRecord setUserStatus(UserState userStatus) {
        if (logDiff("userStatus", this.userStatus, userStatus)) {
            this.userStatus = userStatus;
        }
        return this;
    }

    public WInstallationRecord setUserStatusExpiry(RFC3339 userStatusExpiry) {
        if (logDiff("userStatusExpiry", this.userStatusExpiry, userStatusExpiry)) {
            this.userStatusExpiry = userStatusExpiry;
        }
        return this;
    }

    public WInstallationRecord setFailureCount(Integer failureCount) {
        if (logDiff("failureCount", this.failureCount, failureCount)) {
            this.failureCount = failureCount;
        }
        return this;
    }

    public WInstallationRecord setReportedVersion(String reportedVersion) {
        if (logDiff("reportedVersion", this.reportedVersion, reportedVersion)) {
            this.reportedVersion = reportedVersion;
        }
        return this;
    }

    public WInstallationRecord setTerminal(boolean terminal) {
        if (logDiff("terminal", this.terminal, terminal)) {
            this.terminal = terminal;
        }
        return this;
    }

    public WInstallationRecord setReported(RFC3339 reported) {
        this.reported = reported;
        return this;
    }

    public String getSequence() {
        return sequence;
    }

    public WInstallationRecord setSequence(String sequence) {
        this.sequence = sequence;
        return this;
    }

    public String getUrl() {
        return url;
    }

    public WInstallationRecord setUrl(String url) {
        this.url = url;
        return this;
    }

    public long getStateSequence() {
        return stateSequence;
    }

    public WInstallationRecord setAborted(Boolean aborted) {
        if (logDiff("aborted", this.aborted, aborted)) {
            this.aborted = aborted;
        }
        return this;
    }

    public WInstallationRecord setId(String id) {
        this.id = id;
        return this;
    }

    public WInstallationRecord setError(ClientMessage error) {
        if (error != null) {
            String errorString = error.getString();
            if (logDiff("error", this.error, errorString)) {
                this.error = errorString;
            }
        }
        return this;
    }

    public WInstallationRecord setStateSequence(long stateSequence) {
        this.stateSequence = stateSequence;
        return this;
    }

    @NonNull
    @Override
    protected WInstallationRecord clone() {
        return (WInstallationRecord) LibUtil.reThrow(super::clone);
    }

    /**
     * Produces a version of the WInstallation record that is suitable for sending out on the wire,
     * because the items we store in the queue have additional information. The "right" way to do
     * this would be to have a queue of a different objects that carry WInstallationRecord, but
     * this will require queue migration, which is nastier than doing this.
     * @return a copy of WInstallationRecord that is suitable to be sent out.
     */
    public WInstallationRecord wireFormat() {
        WInstallationRecord copy = clone();
        copy.url = null;
        return copy;
    }

    @Override
    public String objectName() {
        return "componentState";
    }

    public String getError() {
        return error;
    }

    public ComponentState getComponentState() {
        return componentState;
    }

    public OMAUpdateState getUpdateState() {
        return updateState;
    }

    public Integer getDownloadPercent() {
        return downloadPercent;
    }

    public UserState getUserStatus() {
        return userStatus;
    }

    public RFC3339 getUserStatusExpiry() {
        return userStatusExpiry;
    }

    public Integer getFailureCount() {
        return failureCount;
    }

    public String getReportedVersion() {
        return reportedVersion;
    }

    public RFC3339 getReported() {
        return reported;
    }

    public Boolean getAborted() {
        return aborted;
    }

    public boolean isTerminal() {
        return terminal;
    }

    public String getId() {
        return id;
    }

    @Override
    @NonNull
    public String toString() {
        WInstallationRecord copy = clone();
        return JsonThings.gsonNE().toJson(copy);
    }

}
