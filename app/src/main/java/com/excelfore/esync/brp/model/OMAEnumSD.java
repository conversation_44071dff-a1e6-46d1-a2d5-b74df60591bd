package com.excelfore.esync.brp.model;

import com.excelfore.esync.brp.util.LibUtil;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Method;
import java.lang.reflect.Type;

public class OMAEnumSD implements JsonSerializer<NumericOMAEnum>, JsonDeserializer<NumericOMAEnum> {
    @Override
    public NumericOMAEnum deserialize(JsonElement json, Type typeOfT,
                                      JsonDeserializationContext context) throws JsonParseException {

        try {
            //noinspection unchecked,rawtypes
            Method m = ((Class) typeOfT).getDeclaredMethod("getByOmaCode", Integer.TYPE);
            return (NumericOMAEnum) m.invoke(null, json.getAsInt());
        } catch (Throwable e) {
            throw LibUtil.doThrow(e);
        }

    }

    @Override
    public JsonElement serialize(NumericOMAEnum src, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(src.getOmaCode());
    }
}
