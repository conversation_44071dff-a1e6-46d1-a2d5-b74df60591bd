package com.excelfore.esync.brp.model.sync_js;


import com.excelfore.esync.brp.util.RFC3339;

public class WLogDirectoryItem {
    // The length of the log capture, in milliseconds
    private long duration_ms;
    // The length of expiration, in milliseconds
    private long expires_ms;
    // The name of the log file
    private String name;
    // Compressed size of the log file, in bytes
    private long size;
    // Capture started at the time specified
    private RFC3339 startTime;
    // if not null, the log is unreadable, and value indicates why.
    private String unreadable;
    // Number of bytes uploaded so far
    private long uploaded;

    public WLogDirectoryItem(long duration_ms, long expires_ms, String name, long size,
        long startTime, String unreadable, long uploaded) {
        this.duration_ms = duration_ms;
        this.expires_ms = expires_ms;
        this.name = name;
        this.size = size;
        this.startTime = new RFC3339(startTime);
        this.unreadable = unreadable;
        this.uploaded = uploaded;
    }

}
