package com.excelfore.esync.brp.dl;


import com.excelfore.esync.brp.model.sync_js.WDownload;
import com.excelfore.esync.brp.util.ConsumerT;
import com.excelfore.esync.brp.util.DevNullOutputStream;
import com.excelfore.esync.brp.util.RunnableT;

import java.nio.file.Files;

public class NoEncDecrypts extends Decrypts<DevNullOutputStream> {

    public NoEncDecrypts(WDownload dlInfo, Downloader dl, ConsumerT<RunnableT<Exception>, Exception> writeOp, CopyWithMax hash) {
        super(dlInfo, dl, writeOp, hash);
    }

    @Override
    protected void trim() throws Exception {
        writeOp.accept(()->Files.createLink(downloader.updateFile.toPath(), downloader.downloadFile.toPath()));
    }

    @Override
    protected int getBlockSize() {
        return 4096;
    }

    @Override
    protected DevNullOutputStream makeWriter() {
        return new DevNullOutputStream();
    }

    @Override
    protected byte[] decrypt(byte[] input, int len) {
        return null;
    }

    @Override
    protected byte[] finishDecrypt() {
        return null;
    }
}
