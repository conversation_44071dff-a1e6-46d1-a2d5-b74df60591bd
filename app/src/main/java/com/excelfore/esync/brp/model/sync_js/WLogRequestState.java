package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.model.StringEnum;
import com.excelfore.esync.brp.model.StringEnumSD;
import com.google.gson.annotations.JsonAdapter;

@JsonAdapter(StringEnumSD.class)
public enum WLogRequestState implements StringEnum {

    // The device is gathering the log on a previous request
    GATHERING("Gathering"),
    
    // The device has finished log previous log gathering request
    FINISHED("Finished");

    private final String value;

    WLogRequestState(String value) {
        this.value = value;
    }

    @Override
    public String getValue() {
        return value;
    }

    @SuppressWarnings("unused")
    public static final R<WLogRequestState> resolver = new R<>(WLogRequestState.class);


}
