package com.excelfore.esync.brp.model.sync_js;

import com.excelfore.esync.brp.model.NumericOMAEnum;
import com.excelfore.esync.brp.model.OMAEnumSD;
import com.google.gson.annotations.JsonAdapter;

import java.util.HashMap;
import java.util.Map;

@JsonAdapter(OMAEnumSD.class)
public enum OMAUpdateState implements NumericOMAEnum {

    DONE(251),
    PROGRESS(252),
    FAILED(451)
    ;

    int omaCode;

    private final static Map<Integer, OMAUpdateState> byOmaCode =
            new HashMap<>();

    static {
        for (OMAUpdateState ps : OMAUpdateState.values()) {
            byOmaCode.put(ps.omaCode, ps);
        }
    }

    OMAUpdateState(int omaCode) {
        this.omaCode = omaCode;
    }

    public static OMAUpdateState getByOmaCode(int omaCode) {
        OMAUpdateState us = byOmaCode.get(omaCode);
        if (us == null) {
            throw new RuntimeException("Invalid OMA code "+omaCode);
        }
        return us;
    }

    @Override
    public int getOmaCode() {
        return omaCode;
    }
}
