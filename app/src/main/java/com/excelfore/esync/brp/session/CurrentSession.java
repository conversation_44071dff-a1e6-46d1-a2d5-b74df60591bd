package com.excelfore.esync.brp.session;

import com.excelfore.esync.brp.client.BRPUpdateApp;
import com.excelfore.esync.brp.client.SessionWork;
import com.excelfore.esync.brp.util.DescFuture;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Represents currently running session.
 */
public class CurrentSession {

   public final String url;
   public final SessionScheduleRequest origin;
   public final SessionController client;
   public final BRPUpdateApp app;
   private final List<DescFuture<?>> parallelWork = new ArrayList<>();
   private final Object sync = new Object();
   private volatile boolean closed;

   public CurrentSession(SessionController client, SessionScheduleRequest origin, String url) {
      this.client = client;
      this.origin = origin;
      this.url = url;
      this.app = client.app;
   }

   /**
    * Record work item that is a part of the current session. The client is responsible for
    * submitting the specified work on the appropriate executor; this method won't do that.
    * The caller must not submit any tasks for execution, except from inside
    * the specified supplier. Doing otherwise will cause issues, the work
    * may start or even finish executing before it's actually recorded. If that work calls
    * {@link #closeWork()}, the recording will fail with an exception, in which case
    * actual client will restart, and, if running in the test framework, lead to test failures.
    * <br><br>
    * In other words, this code is OK:
    * <pre>
    *     recordWork(()->return new DescFuture("good code", executor.submit(codeToRun)));
    * </pre>
    * But this one is <b>NOT</b>:
    * <pre>
    *     DescFuture<?> f = new DescFuture("bad code", executor.submit(codeToRun));
    *     recordWork(()->f);
    * </pre>
    * The way that using a supplier fixes the problem, is by having the
    * supplier invoked while holding a lock; if the execution the of corresponding work causes
    * the session to close before the work recorded, the lock will prevent that closure to
    * register before said recording. Recording would happen first, and attempt to close
    * will wait until the lock is released.
    * <br><br>
    * It's generally recommended using {@link SessionWork#submitWork(String, Callable)}
    * and {@link SessionWork#submitWork(String, Runnable)} methods instead of this one.
    * @param workMaker supplier that shall schedule work when invoked, and return a future
    * that resolves when that work is done.
    */
   public void recordWork(Supplier<DescFuture<?>> workMaker) {

      synchronized (sync) {

         DescFuture<?> df = workMaker.get();

         if (closed) {
            throw new RuntimeException("Can not record work "+df.name+", session is closed");
         }

         parallelWork.add(df);

      }

   }

   public List<DescFuture<?>> closeWork() {

      synchronized (sync) {
         closed = true;
      }

      return parallelWork;

   }
}
