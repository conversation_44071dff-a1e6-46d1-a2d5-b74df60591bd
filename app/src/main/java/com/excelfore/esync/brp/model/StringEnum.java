package com.excelfore.esync.brp.model;

import java.util.HashMap;
import java.util.Map;

public interface StringEnum {

    String getValue();

    class R<Y extends Enum<Y> & StringEnum> {

        private final Map<String, Y> cache = new HashMap<>();

        public R(Class<Y> enumClass) {
            for (Y y : enumClass.getEnumConstants()) {
                cache.put(y.getValue(), y);
            }
        }

        public Y fromString(String s) {

            return cache.get(s);

        }

    }

}
