package com.excelfore.esync.brp.client;


import static com.excelfore.esync.brp.Constants.TAG;

import com.excelfore.esync.brp.util.L;
import com.excelfore.esync.brp.util.LibUtil;

import org.bouncycastle.asn1.DEROctetString;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.asn1.x509.GeneralName;
import org.bouncycastle.asn1.x509.GeneralNames;

import java.security.cert.CRL;
import java.security.cert.CertPathValidatorException;
import java.security.cert.Certificate;
import java.security.cert.PKIXCertPathChecker;
import java.security.cert.X509CRL;
import java.security.cert.X509CRLEntry;
import java.security.cert.X509Certificate;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;

import javax.security.auth.x500.X500Principal;

// some of this code is copied from org.bouncycastle.pkix.jcajce.X509RevocationChecker
// but we couldn't use that, because vendors don't necessarily give us the CA to validate the CRL
// itself against, and we don't need support for distribution points, etc.
public class CRLChecker extends PKIXCertPathChecker {

    private final X509CRL crl;

    private Date currentDate;

    public CRLChecker(CRL crl) {
        if (!(crl instanceof X509CRL)) {
            throw new IllegalArgumentException("Only X509 CRLs are supported");
        }
        this.crl = (X509CRL) crl;
    }

    @Override
    public void init(boolean forward) {
        if (forward) {
            throw new IllegalArgumentException("forward processing not supported");
        }

        this.currentDate = new Date();
    }

    @Override
    public boolean isForwardCheckingSupported() {
        return false;
    }

    @Override
    public Set<String> getSupportedExtensions() {
        return null;
    }

    @Override
    public void check(Certificate certificate, Collection<String> collection)
            throws CertPathValidatorException {

        X509Certificate cert = (X509Certificate) certificate;
        L.d(TAG, "Validating cert "+cert.getSubjectX500Principal()+
                " issued by "+cert.getIssuerX500Principal());

        if (crl.getRevokedCertificates() == null) { return; }

        for (X509CRLEntry revokedCertificate : crl.getRevokedCertificates()) {

            if (currentDate.before(revokedCertificate.getRevocationDate())) {
                L.d(TAG, "Certificate is not yet revoked");
                continue;
            }

            if (!Objects.equals(revokedCertificate.getSerialNumber(), cert.getSerialNumber())) {
                L.d(TAG, "Serial numbers mismatch : revoked="+
                        revokedCertificate.getSerialNumber() + ", checked="+cert.getSerialNumber());
                continue;
            }

            // we need to verify the issuer, because certs being validated here may have come
            // from different CAs, and therefore the serial numbers might clash.
            // $TODO: we can't reliably use revokedCertificate.getCertificateIssuer(),
            // because on "direct" CRLs, this always returns null. I don't know how generate
            // "indirect" CRL objects, because that parameter is in the CRL distribution point,
            // which we don't have, as we start from a bare CRL. So we have to dig through the
            // extensions directly for it.

            X500Principal issuer = null;
            byte [] rawIssuerExt = revokedCertificate.getExtensionValue(Extension.certificateIssuer.getId());
            if (rawIssuerExt != null) {
                GeneralNames gn = GeneralNames.getInstance(DEROctetString.getInstance(rawIssuerExt).getOctets());
                GeneralName[] names = gn.getNames();
                for (GeneralName generalName : names) {
                    if (generalName.getTagNo() == GeneralName.directoryName) {
                        X500Name name = X500Name.getInstance(generalName.getName());
                        issuer = new X500Principal(LibUtil.reThrow((Callable<byte[]>) name::getEncoded));
                    }
                }
            }

            if (issuer == null) {
                L.w(TAG, "No CRL entry issuer found, using CRL issuer");
                issuer = crl.getIssuerX500Principal();
            }

            if (!Objects.equals(cert.getIssuerX500Principal(), issuer)) {
                L.d(TAG, "Issuer mismatch : revoked="+
                        issuer + ", checked="+cert.getIssuerX500Principal());
                continue;
            }

            throw new CertPathValidatorException("Certificate "+cert.getSubjectX500Principal()+
                    ", s/n "+cert.getSerialNumber()+" has been revoked. Recorded reason: "+
                    revokedCertificate.getRevocationReason());

        }
    }

}
