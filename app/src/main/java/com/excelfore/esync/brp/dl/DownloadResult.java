package com.excelfore.esync.brp.dl;

import com.excelfore.esync.brp.session.OperationResult;
import com.excelfore.esync.brp.util.CFormat;
import com.excelfore.esync.brp.util.MyUtil;

import java.io.File;

public class DownloadResult extends OperationResult {

    private boolean rangeError;
    private File downloadedFile;
    private long bytesDownloaded;
    private ContentRangeHeader crh;
    private boolean countNoVerificationFailure;
    private boolean isVerificationError;
    private String url;

    public boolean isRangeError() {
        return rangeError;
    }

    public DownloadResult setRangeError(boolean rangeError) {
        this.rangeError = rangeError;
        return this;
    }

    public File getDownloadedFile() {
        return downloadedFile;
    }

    public DownloadResult setDownloadedFile(File downloadedFile) {
        this.downloadedFile = downloadedFile;
        return this;
    }

    public String downloadedDetails() {
        return MyUtil.ifNotNull(crh,
                crh-> new CFormat(" (downloaded %,d range %,d to %,d, len %,d)").format(bytesDownloaded, crh.getFrom(), crh.getTo(), crh.getTotal()),
                ()->"");
    }

    public DownloadResult setBytesDownloaded(long bytesDownloaded) {
        this.bytesDownloaded = bytesDownloaded;
        return this;
    }

    public DownloadResult setCrh(ContentRangeHeader crh) {
        this.crh = crh;
        return this;
    }

    public long getBytesDownloaded() {
        return bytesDownloaded;
    }

    public boolean isCountNoVerificationFailure() {
        return countNoVerificationFailure;
    }

    public DownloadResult setCountNoVerificationFailure(boolean countNoVerificationFailure) {
        this.countNoVerificationFailure = countNoVerificationFailure;
        return this;
    }

    public boolean isVerificationError() {
        return isVerificationError;
    }

    public void setVerificationError(boolean verificationError) {
        isVerificationError = verificationError;
    }

    @Override
    public boolean isDownload() {
        return true;
    }

    public DownloadResult setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getUrl() {
        return url;
    }
}
