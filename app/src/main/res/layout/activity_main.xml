<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".client.MainActivity">

    <TextView
        android:id="@+id/hello"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.110000014" />

    <ToggleButton
        android:id="@+id/intentType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:text="ToggleButton"
        android:textOff="@string/intent_intent"
        android:textOn="@string/intent_am"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@+id/hello"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.971" />

    <Button
        android:id="@+id/cycle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cycle"
        app:layout_constraintBottom_toBottomOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="1dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hello"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/verLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:labelFor="@id/verValue"
            android:text="@string/version_label" />

        <TextView
            android:id="@+id/verValue"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/successful"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/update_successful"
            android:visibility="invisible" />

        <LinearLayout
            android:id="@+id/available"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="120dp"
                android:text="@string/update_prompt" />

            <TextView
                android:id="@+id/update_to"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_weight="1" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/consent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <Button
                android:id="@+id/agree"
                style="@android:style/Widget.Button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxWidth="120dip"
                android:text="@string/button_agree"
                android:textColor="?attr/colorPrimaryDark" />

            <Button
                android:id="@+id/disagree"
                style="@android:style/Widget.Button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxWidth="120dip"
                android:text="@string/button_disagree"
                android:textColor="@android:color/holo_red_dark" />
        </LinearLayout>

        <ProgressBar
            android:id="@+id/progressBar"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:progress="0"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/dlnSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="monospace"
                android:text="@string/wait" />

            <TextView
                android:id="@+id/rSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="monospace"
                android:text="@string/wait" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/dlwSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="monospace"
                android:text="@string/wait" />

            <TextView
                android:id="@+id/wSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="monospace"
                android:text="@string/wait" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/dlSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="monospace"
                android:text="@string/wait" />

            <TextView
                android:id="@+id/rwSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="monospace"
                android:text="@string/wait" />
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
