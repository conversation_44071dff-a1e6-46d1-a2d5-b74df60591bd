package com.excelfore.esync.brp.fwk;

import com.excelfore.esync.brp.util.LibUtil;

import org.junit.Assert;
import org.junit.Test;

public class LibUtilTest {

    @Test
    public void hex() {

        byte [] b = new byte[] {
                (byte)0xa0,
                (byte)0xb1,
                (byte)0xc2,
                (byte)0xd3,
                (byte)0xe4,
                (byte)0xf5,
                (byte)0x6a,
                (byte)0x7b,
                (byte)0x8c,
                (byte)0x9d,
                (byte)0xae,
                (byte)0xbf,
                (byte)0xff,
                (byte)0x00,
                (byte)0x41
        };

        Assert.assertEquals("a0b1c2d3e4f56a7b8c9daebfff0041", LibUtil.hexString(b));

    }

}
