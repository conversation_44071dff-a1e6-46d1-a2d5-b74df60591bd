package com.excelfore.esync.brp;

import android.widget.TextView;

import com.excelfore.esync.brp.client.MainActivity;
import com.excelfore.esync.brp.fwk.FakeAndroidKeyStore;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.android.controller.ActivityController;

@RunWith(RobolectricTestRunner.class)
public class SmokeTest {

    @Test
    public void test() {

        try (ActivityController<MainActivity> main = Robolectric.buildActivity(MainActivity.class)) {

            main.create();
            main.start();
            TextView tv = main.get().findViewById(R.id.hello);
            Assert.assertEquals("BRP eSync OTA Client", tv.getText());

        }

    }

    @BeforeClass
    public static void setupKS() {

        FakeAndroidKeyStore.INSTANCE.getSetup();

    }

}