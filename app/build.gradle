import com.excelfore.esync.agent.bld.BU
import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

apply plugin: 'com.android.application'
apply plugin: 'org.owasp.dependencycheck'
apply plugin: 'com.google.android.gms.oss-licenses-plugin'
apply from: '../jacoco/modules.gradle'
apply plugin: 'org.jetbrains.kotlin.android'

// $TODO UNCOMMENT AND FIX VULNERABILITIES!
// There is a ton flagged right away, IDK where they even come from
// assemble.dependsOn dependencyCheckAnalyze

buildscript {
    repositories {
        mavenCentral()
    }
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
        vendor.set(JvmVendorSpec.AMAZON)
    }
}

def generatedSchemaDir = "${projectDir}/build/schemas"
def storedSchemaDir = "${projectDir}/src/test/schemas"

android {

    buildToolsVersion "35.0.0"

    buildFeatures {
        buildConfig = true
    }

    namespace = "com.excelfore.esync.brp"
    compileSdkVersion = 33

    configurations{
        // exclude old bouncycastle libs, or else we get those and ours in the APK
        all*.exclude module: 'bcprov-jdk15on'
        all*.exclude module: 'bcpkix-jdk15on'
        all*.exclude module: 'bctls-jdk15on'
        all*.exclude module: 'bcprov-jdk18on'
        all*.exclude module: 'bcpkix-jdk18on'
        all*.exclude module: 'bctls-jdk18on'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }

    defaultConfig {
        applicationId "com.excelfore.esync.brp"

        minSdk = 33
        targetSdk = 33

        versionName useVersionName

        buildConfigField("String", "GIT_VERSION", BU.escapeStr(androidGitVersion.name()))

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        lintOptions {
            abortOnError false
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": generatedSchemaDir.toString() ]
            }
        }

    }

    flavorDimensions "update"

    productFlavors {
        v1 {
            dimension "update"
            versionNameSuffix ""
            versionCode useVersionCode * 2
        }
        /*
        v2 {
            dimension "update"
            versionNameSuffix "-v2"
            versionCode useVersionCode * 2 + 1
        }
         */

    }

    buildTypes {

        release {
            testCoverageEnabled = false
            minifyEnabled false
        }

        applicationVariants.all { variant ->

            def btComp = BU.capitalize(variant.buildType.name)
            println("Variant "+variant.flavorName+", build type "+btComp)

            logger.info(variant.flavorName + ':' + variant.buildType.name)
            // for some reason, Android room uses really old dependencies for annotation processors
            // It's OK to disable this configuration, as it's fully self-contained, unless, however,
            // they would produce vulnerable class code at the end of processing.
            dependencyCheck.skipConfigurations += [ variant.flavorName + btComp + "AnnotationProcessorClasspath" ]

            variant.outputs.all { output ->
                outputFileName = "brp-esync.apk"
            }

        }
    }

    task printConfigurations {
        doLast {task ->
            println "Project Name: $project.name configurations:"
            configurations.each {
                println "    $it.name"
            }
            println "Variants:"
            applicationVariants.all {
                v -> println("* "+v.flavorName + ':' + v.buildType.name)
            }
        }
    }
    kotlinOptions {
        jvmTarget = '11'
    }

    // this block (creating muppet.bin) is to inflate the size of the APK
    // so it's a better show case for the OTA (palpable installtion time)
    // https://www.jbamberger.de/development/2021/07/03/android-generating-asset-files.html
    sourceSets["main"].resources.srcDir(
        layout.buildDirectory.dir("generated/dependencyResources/"))

    aaptOptions {
        noCompress 'grb'
    }

    /* - needed for BRP-8 only - to inflate the size of the app so that
         it takes measurable time to install.
    applicationVariants.configureEach { variant ->

        def customTask = tasks.register("generate${variant.name.capitalize()}ResourceFiles") {
            doLast {
                def dir = layout.buildDirectory.dir("generated/dependencyResources/").get()
                dir.getAsFile().mkdirs()
                def f = dir.file("muppet.grb").getAsFile()
                byte[] bytes = new byte[32];
                def sha = bytes.digest('SHA-256')
                new Random().nextBytes(bytes);
                f.withWriter { w->
                    // this magic# makes it ~80Mb
                    for (def i = 0; i<1310720; i++) {
                        w.write(sha)
                        sha = sha.digest('SHA-256')
                    }
                }
            }
        }
        tasks["merge${variant.name.capitalize()}Resources"].dependsOn(customTask)

    }
     */


}

dependencies {

    // we need Guava for a couple of really silly things, but most notably
    // their actual implementations of Listenable future, which is not provided
    // in the concurrent package :(
    implementation("com.google.guava:guava:${versions.guava}") {
        exclude group: 'org.checkerframework', module: 'checker-compat-qual'
    }

    implementation "androidx.appcompat:appcompat:${versions.appCompat}"
    implementation "androidx.constraintlayout:constraintlayout:${versions.constraintLayout}"
    implementation "org.bouncycastle:bcpkix-jdk15to18:${versions.bc}"
    implementation "org.bouncycastle:bctls-jdk15to18:${versions.bc}"
    implementation "org.bouncycastle:bcprov-jdk15to18:${versions.bc}"
    implementation "com.android.volley:volley:${versions.volley}"
    implementation "androidx.security:security-crypto:${versions.securityCrypto}"
    implementation "com.google.code.gson:gson:${versions.gson}"
    implementation "com.squareup.okhttp3:okhttp:${versions.okhttp}"
    implementation "androidx.room:room-runtime:${versions.room}"
    implementation "com.squareup.tape2:tape:${versions.tape2}"
    implementation "androidx.core:core-ktx:${versions.ktx}"

    annotationProcessor "androidx.room:room-compiler:${versions.room}"

    testImplementation "junit:junit:${versions.test.junit}"
    testImplementation "org.robolectric:robolectric:${versions.test.robolectric}"
    testImplementation "androidx.room:room-testing:${versions.test.room_testing}"

}





// https://stackoverflow.com/a/36130467/622266
// this produces a continuous log output that is *really* helpful to
// debug problems related to inter-test problems.
tasks.withType(Test) {
    testLogging {
        // set options for log level LIFECYCLE
        events TestLogEvent.FAILED,
                TestLogEvent.PASSED,
                TestLogEvent.SKIPPED,
                TestLogEvent.STANDARD_OUT
        exceptionFormat TestExceptionFormat.FULL
        showExceptions true
        showCauses true
        showStackTraces true

        // set options for log level DEBUG and INFO
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_ERROR,
                    TestLogEvent.STANDARD_OUT
            exceptionFormat TestExceptionFormat.FULL
        }
        info.events = debug.events
        info.exceptionFormat = debug.exceptionFormat
    }
}

dependencyCheck {
    autoUpdate = true
    format = 'HTML'
    skipConfigurations += ['lintClassPath']
    failBuildOnCVSS = 0
    scanSet = ['dependencies']
    suppressionFile = 'https://dev-esync.excelfore.com/odd_files/dcm.suppression.xml'
}
